import { z } from "zod";

export const basicFileSchema = z.object({
  id: z.number(),
  name: z.string().max(256),
  url: z.string().max(1024),
  userId: z.string().max(256),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime().nullable(),
  author: z.string().max(256),
  authorSite: z.string().max(256),
  mainCategory: z.string().max(256),
  difficulty: z.string().max(50),
  garmentType: z.string().max(100),
  yarnWeight: z.string().max(50),
  ageGroup: z.string().max(50),
  thumbnailUrl: z.string().max(1024).nullable(),
});

export const basicFileListSchema = z.array(basicFileSchema);

export const patternDetailsSchema = basicFileSchema.extend({
  seasons: z.array(z.string()),
  sizes: z.array(z.string()),
  tags: z.array(z.string().max(50)).min(2).max(6),
});
