import { sql } from "drizzle-orm";
import {
  index,
  pgTableCreator,
  serial,
  timestamp,
  varchar,
  integer,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { z } from "zod";

export const createTable = pgTableCreator((name) => `pitterpattern_${name}`);

export const pdfs = createTable("pdf", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 256 }).notNull(),
  url: varchar("url", { length: 1024 }).notNull(),
  userId: varchar("userId", { length: 256 }).notNull(),
  createdAt: timestamp("created_at", { withTimezone: true })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).$onUpdate(
    () => new Date(),
  ),
  author: varchar("author", { length: 256 }).notNull(),
  authorSite: varchar("author_site", { length: 256 }).notNull(),
  mainCategory: varchar("main_category", { length: 256 }).notNull(),
  difficulty: varchar("difficulty", { length: 50 }).notNull(),
  garmentType: varchar("garment_type", { length: 100 }).notNull(),
  yarnWeight: varchar("yarn_weight", { length: 50 }).notNull(),
  ageGroup: varchar("age_group", { length: 50 }).notNull(),
  thumbnailUrl: varchar("thumbnail_url", { length: 1024 }),
});

export const pdfRelations = relations(pdfs, ({ many }) => ({
  pdfSeasons: many(pdfSeasons),
  pdfSizes: many(pdfSizes),
  pdfTags: many(pdfTags),
}));

export const seasons = createTable("season", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 50 }).notNull().unique(),
});

export const pdfSeasons = createTable("pdf_season", {
  pdfId: integer("pdf_id")
    .references(() => pdfs.id, { onDelete: "cascade" })
    .notNull(),
  seasonId: integer("season_id")
    .references(() => seasons.id, { onDelete: "cascade" })
    .notNull(),
});

export const pdfSeasonsIndex = index("pdf_seasons_pk").on(
  pdfSeasons.pdfId,
  pdfSeasons.seasonId,
);

export const pdfSeasonsRelations = relations(pdfSeasons, ({ one }) => ({
  pdf: one(pdfs, {
    fields: [pdfSeasons.pdfId],
    references: [pdfs.id],
  }),
  season: one(seasons, {
    fields: [pdfSeasons.seasonId],
    references: [seasons.id],
  }),
}));

export const sizes = createTable("size", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 50 }).notNull().unique(),
});

export const pdfSizes = createTable("pdf_size", {
  pdfId: integer("pdf_id")
    .references(() => pdfs.id, { onDelete: "cascade" })
    .notNull(),
  sizeId: integer("size_id")
    .references(() => sizes.id, { onDelete: "cascade" })
    .notNull(),
});

export const pdfSizesIndex = index("pdf_sizes_pk").on(
  pdfSizes.pdfId,
  pdfSizes.sizeId,
);

export const pdfSizesRelations = relations(pdfSizes, ({ one }) => ({
  pdf: one(pdfs, {
    fields: [pdfSizes.pdfId],
    references: [pdfs.id],
  }),
  size: one(sizes, {
    fields: [pdfSizes.sizeId],
    references: [sizes.id],
  }),
}));

export const tags = createTable("tag", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 50 }).notNull().unique(),
});

export const pdfTags = createTable("pdf_tag", {
  pdfId: integer("pdf_id")
    .references(() => pdfs.id, { onDelete: "cascade" })
    .notNull(),
  tagId: integer("tag_id")
    .references(() => tags.id, { onDelete: "cascade" })
    .notNull(),
});

export const pdfTagsIndex = index("pdf_tags_pk").on(
  pdfTags.pdfId,
  pdfTags.tagId,
);

export const pdfTagsRelations = relations(pdfTags, ({ one }) => ({
  pdf: one(pdfs, {
    fields: [pdfTags.pdfId],
    references: [pdfs.id],
  }),
  tag: one(tags, {
    fields: [pdfTags.tagId],
    references: [tags.id],
  }),
}));

export const patternAnalysisSchema = z.object({
  name: z.string(),
  author: z.string(),
  authorSite: z.string(),
  mainCategory: z.string(),
  difficulty: z.string(),
  garmentType: z.string(),
  yarnWeight: z.string(),
  ageGroup: z.string(),
  seasons: z.array(z.string()),
  sizes: z.array(z.string()),
  tags: z.array(z.string()).min(2).max(6),
});

export const InsertPdfSchema = patternAnalysisSchema.extend({
  userId: z.string(),
  url: z.string(),
  thumbnailUrl: z.string(),
  createdAt: z.string().datetime().optional(),
});

export type FileInsertData = z.infer<typeof InsertPdfSchema>;
export type FileData = Omit<FileInsertData, "createdAt"> & {
  id: number;
  createdAt: NonNullable<FileInsertData["createdAt"]>;
};
