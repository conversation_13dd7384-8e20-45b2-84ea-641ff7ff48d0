"use client";
import type { PatternAnalysis } from "~/lib/api/external/ai-file-info/generate-pdf-analysis";
import { patternAnalysisSchema } from "~/lib/api/external/ai-file-info/generate-pdf-analysis";
import { extractPdfText } from "~/lib/pdf-utils";

export const getAiAnalysis = async (
  pdfFile: File,
): Promise<PatternAnalysis | undefined> => {
  try {
    const pdfText = await extractPdfText(pdfFile);

    const formData = new FormData();
    formData.append("pdf", pdfFile);
    formData.append("pdfText", pdfText);

    const res = await fetch("/api/ai-api", {
      method: "POST",
      body: formData,
    });
    if (!res.ok) {
      throw new Error("AI API Frontend Error");
    }
    const response: unknown = await res.json();
    if (
      typeof response === "object" &&
      response !== null &&
      "data" in response
    ) {
      const validatedData = patternAnalysisSchema.parse(response.data);
      return validatedData;
    }
    throw new Error("Invalid response format");
  } catch (error) {
    console.error("Error fetching AI info:", error);
    throw error;
  }
};
