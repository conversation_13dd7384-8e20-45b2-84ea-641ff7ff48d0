import React from "react";
import { Card } from "~/components/ui/card";

/**
 * ThumbnailSkeleton component for displaying a loading state for PDF thumbnails
 * This is used within the gallery loading skeleton
 */
export const ThumbnailSkeleton = () => (
  <div className="relative h-full w-full overflow-hidden rounded-lg">
    {/* Base background */}
    <div className="bg-primary/10 absolute inset-0"></div>

    {/* Animated background with shimmer effect */}
    <div
      className="from-primary/5 via-primary/30 to-primary/5 animate-shimmer absolute inset-0 bg-gradient-to-r"
      style={{ backgroundSize: "200% 100%" }}
    />

    {/* Pulsing overlay */}
    <div className="bg-primary/10 absolute inset-0 animate-pulse"></div>

    {/* Text placeholders */}
    <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/70 to-transparent p-3">
      <div className="h-5 w-full animate-pulse rounded-md bg-white/50" />
      <div className="mt-1 h-4 w-3/4 animate-pulse rounded-md bg-white/50" />
      <div className="mt-1 h-4 w-5/6 animate-pulse rounded-md bg-white/50" />
    </div>
  </div>
);

/**
 * GallerySkeleton component for displaying a grid of PDF thumbnail skeletons
 * Used in the gallery view when files are loading
 */
export const GallerySkeleton: React.FC = () => {
  return (
    <div className="flex flex-wrap justify-center gap-4 p-4">
      {Array.from({ length: 12 }).map((_, i) => (
        <div key={i} className="w-48">
          <div className="h-64 w-48">
            <Card className="relative h-full w-full overflow-hidden p-0">
              <ThumbnailSkeleton />
            </Card>
          </div>
        </div>
      ))}
    </div>
  );
};
