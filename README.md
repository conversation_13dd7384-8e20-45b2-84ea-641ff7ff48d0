# Pitter Pattern Test

## TODO

- [x] Make it deploy (vercel)
- [x] Scaffold basic ui with mock data
- [x] Tidy up build prcess
- [x] Actually set up a database (vercel postgres)
- [x] Attach DB to UI
- [x] Add auth (clerk)
- [x] Add image file upload
- [x] Use Next Image component (used for gallery)
- [x] Error management (Sentry)
- [x] Routing/image page (parallel route)
- [x] ShadUI init (for toasts/sonner)
- [x] Analytics (posthog)
- [x] Delete button (server action)
- [x] Ratelimiting (upstash)
- [-] setup github actions CI (github repo needs to be public or upgraded)
- [x] Swap image to PDF
- [x] Add PDF preview / view
- [x] Set up PDF view PDF page navigation
- [x] fix navigation directly to file number without click-through
- [x] Connect server to AI API (?)
- [x] Set up PDF text reader to provide data to AI prompt
- [-] Upgrade Node version to 22 (to avoid pdfjs "Promise.withResolvers" build bug) -- not supported on vercel
- [x] uninstall pdfjs full package if not needed (installed for the worker src to solve Promse.withResolvers bug, but it didnt work)
- [x] adjust database structure for PDF/pattern data
- [x] Change image db to pdf db
- [x] Connect pdf-ai component to original file upload component
- [x] make pdf upload page only refresh once and have better success toast tracking (currently only runs with upload, not processing and DB)
- [-] add "skills/techniques" property to pattern data (currently too inaccurate -- AI needs to be better / with an addition of a skill/technique list)
- [x] add file image loading component / skeleton box in gallery
- [x] make pattern data page or component wait for loaded pdf (or use some loader box)
- [x] add tables in place of object array - migrated to new db structure
- [x] use react query for main data queries
- [x] gallery item selection/multiselect with state (zustand?)
- [x] for filters with only one option, replace "All" with the option label and some indication that there is no dropdown
- [x] implement PDF thumbnail images? the PDF reloads when filtering and rerendering (yikes!)
- [x] fix gallery loading skeleton
- [x] remove pdf viewer loading flicker
- [ ] handle upload failures (stuck at upload loading UI)
- [ ] handle invalid pdf uploads and "<UNKNOWN>" ai api data
- [ ] set up back-up api request in case claude is down
- [ ] handle on delete - when no files result from current filters anymore (eg filters empty state page)
- [ ] make pdf view responsive (currently too narrow, needs to get size from PDF or PDF needs to shrink to fit it height AND width)
- [ ] use message batches to handle multiple simultaneous uploads
- [ ] handle upload failures (stuck at upload loading UI)
- [ ] prettier login page -- clerk screen for prod on custom domain
- [ ] add full screen view PDF view modal
- [ ] add page flip animation to PDF view (turnjs?)
- [ ] error-handling in UI (eg viewing file route without auth "unauthorized")
- [ ] handle duplicate uploads (file name?)
- [ ] show preview or "cheat" array push of uploading files
- [ ] Set up page layout for images of different resolutions
- [ ] infinite scroll
- [ ] folders/albums (change DB structure)
- [ ] set up custom domain and connect to vercel
- [ ] move to Next Auth instead of Clerk Production
- [ ] handle "unhandled runtime errors" from showing in app instead of the sentry error page
- [ ] use metadata to set upload file type
- [ ] set up mobile-first layout
- [ ] payment flow/processing for pitterpatternpremium
- [ ] profile details / modification
