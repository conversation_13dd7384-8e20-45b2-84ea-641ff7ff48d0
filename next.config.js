/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
await import("./src/env.js");

/** @type {import("next").NextConfig} */
const coreConfig = {
    images: {
        remotePatterns: [{ hostname: "utfs.io" }],
    },
    typescript: {
        ignoreBuildErrors: true,
    },
    eslint: {
        ignoreDuringBuilds: true
    },
    // If you use Next.js with Turbopack enabled:
    experimental: {
        turbo: {
          resolveAlias: {
            canvas: './empty-module.ts',
            // Removed 'pdfjs-dist' alias since we now use react-pdf's bundled version
          },
        },
        // Add this to optimize PDF.js imports
        optimizePackageImports: ['react-pdf'],
    },
    // Add this to exclude problematic packages from server bundling
    serverExternalPackages: [
        'canvas',
        // Removed 'pdfjs-dist' since we use react-pdf's bundled version
    ],
    transpilePackages: [
        'import-in-the-middle',
        'require-in-the-middle',
    ],
    async rewrites() {
        return [
          {
            source: "/ingest/static/:path*",
            destination: "https://us-assets.i.posthog.com/static/:path*",
          },
          {
            source: "/ingest/:path*",
            destination: "https://us.i.posthog.com/:path*",
          },
          {
            source: "/ingest/decide",
            destination: "https://us.i.posthog.com/decide",
          },
        ];
    },
    // This is required to support PostHog trailing slash API requests
    skipTrailingSlashRedirect: true, 
};


import { withSentryConfig } from "@sentry/nextjs";

/** @type {import("next").NextConfig} */
const config = withSentryConfig(coreConfig,
    {
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options

    org: "daniel-greenberg",
    project: "pitterpattern-demo",

    // Only print logs for uploading source maps in CI
    silent: !process.env.CI,

    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Automatically annotate React components to show their full name in breadcrumbs and session replay
    reactComponentAnnotation: {
      enabled: true,
    },

    // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
    // This can increase your server load as well as your hosting bill.
    // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
    // side errors will fail.
    tunnelRoute: "/monitoring",

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,

    // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
    // See the following for more information:
    // https://docs.sentry.io/product/crons/
    // https://vercel.com/docs/cron-jobs
    automaticVercelMonitors: false, // set to false because this app uses App Router
    });

export default config;
