import { NextResponse } from "next/server";
import { getUserFiles } from "~/server/queries";

export async function GET() {
  try {
    const files = await getUserFiles();
    return NextResponse.json(files);
  } catch (error) {
    if (error instanceof Error && error.message === "Unauthorized") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    console.error("Error fetching files:", error);
    return NextResponse.json(
      { error: "Failed to fetch files" },
      { status: 500 },
    );
  }
}
