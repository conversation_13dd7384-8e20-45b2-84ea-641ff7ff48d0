"use client";

import { useState } from "react";
import { Input } from "~/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import type { FilterState, FilterKey, CategoryFilterData } from "./FileGallery";
import { Label } from "~/components/ui/label";

const formatCategoryName = (name: string): string => {
  // Insert a space before each capital letter and capitalize the first letter
  return name
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase());
};

export type FilterBarProps = {
  onFilterChange: (filters: FilterState) => void;
  onSortChange: (sort: string) => void;
  categoryFilterData: CategoryFilterData;
};

const SearchBar = ({
  searchValue,
  handleFilterChange,
}: {
  searchValue: FilterState["search"];
  handleFilterChange: (key: keyof FilterState, value: string) => void;
}) => {
  return (
    <div className="flex w-full flex-col gap-1.5 sm:w-64">
      <Label htmlFor="search-input" className="text-xs text-muted-foreground">
        Search
      </Label>
      <Input
        id="search-input"
        placeholder="Search files..."
        value={searchValue}
        onChange={(e) => handleFilterChange("search", e.target.value)}
        className="w-full"
      />
    </div>
  );
};

const SortDropdown = ({
  onSortChange,
}: {
  onSortChange: FilterBarProps["onSortChange"];
}) => {
  return (
    <div className="flex w-full flex-col gap-1.5 sm:w-40">
      <Label htmlFor="sort-select" className="text-xs text-muted-foreground">
        Sort By
      </Label>
      <Select onValueChange={onSortChange}>
        <SelectTrigger id="sort-select" className="w-full">
          <SelectValue placeholder="Select order" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="name_asc">Name (A-Z)</SelectItem>
          <SelectItem value="name_desc">Name (Z-A)</SelectItem>
          <SelectItem value="date_asc">Upload Date (Oldest)</SelectItem>
          <SelectItem value="date_desc">Upload Date (Newest)</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

const CategoryFilter = ({
  filters,
  category,
  handleFilterChange,
  categoryFilterData,
}: {
  category: FilterKey;
  filters: FilterState;
  handleFilterChange: (key: keyof FilterState, value: string) => void;
  categoryFilterData: CategoryFilterData;
}) => {
  return (
    <div key={category} className="flex w-full flex-col gap-1.5 sm:w-40">
      <Label
        htmlFor={`filter-${category}`}
        className="text-xs text-muted-foreground"
      >
        {formatCategoryName(category)}
      </Label>
      <Select
        value={filters[category]}
        onValueChange={(value) =>
          typeof value === "string" && handleFilterChange(category, value)
        }
      >
        <SelectTrigger id={`filter-${category}`} className="w-full">
          <SelectValue placeholder={`Select ${formatCategoryName(category)}`} />
        </SelectTrigger>
        <SelectContent>
          {categoryFilterData[category].map((item) => (
            <SelectItem key={item} value={item}>
              {item.charAt(0).toUpperCase() + item.slice(1)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export function FilterBar({
  onFilterChange,
  onSortChange,
  categoryFilterData,
}: FilterBarProps) {
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    mainCategory: "All",
    difficulty: "All",
    garmentType: "All",
    yarnWeight: "All",
    ageGroup: "All",
  });

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  return (
    <div className="mb-4 flex flex-wrap items-center gap-4 rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
      <SearchBar
        searchValue={filters.search}
        handleFilterChange={handleFilterChange}
      />

      {(Object.keys(categoryFilterData) as Array<FilterKey>).map((category) => (
        <CategoryFilter
          key={category}
          filters={filters}
          category={category}
          handleFilterChange={handleFilterChange}
          categoryFilterData={categoryFilterData}
        />
      ))}

      <SortDropdown onSortChange={onSortChange} />
    </div>
  );
}
