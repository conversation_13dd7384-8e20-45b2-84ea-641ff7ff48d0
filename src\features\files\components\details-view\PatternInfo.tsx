"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import type { PatternAnalysis } from "~/lib/api/external/ai-file-info/generate-pdf-analysis";

export function PatternInfoComponent({
  patternData,
}: {
  patternData: PatternAnalysis;
}) {
  const propsToDisplay: Array<keyof PatternAnalysis> = [
    "authorSite",
    "difficulty",
    "garmentType",
    "yarnWeight",
    "ageGroup",
    "seasons",
    "sizes",
  ];

  const ensureFullyQualifiedUrl = (url: string) =>
    url.startsWith("http") ? url : `https://${url}`;

  const formatValue = (key: keyof PatternAnalysis, value: any) => {
    if (Array.isArray(value)) {
      return (
        <div className="flex flex-wrap gap-1">
          {value.map((item, index) => (
            <Badge key={index} variant="outline">
              {item}
            </Badge>
          ))}
        </div>
      );
    }

    if (key === "authorSite" && value) {
      return (
        <a
          href={ensureFullyQualifiedUrl(value)}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-500 hover:underline"
        >
          {value}
        </a>
      );
    }

    return value;
  };

  const formatKey = (key: string) => {
    // Convert camelCase to Title Case with spaces
    return (
      key
        // Insert a space before all caps
        .replace(/([A-Z])/g, " $1")
        // Uppercase the first character
        .replace(/^./, (str) => str.toUpperCase())
    );
  };

  return (
    <Card className="border-0 bg-card shadow-none">
      <CardHeader>
        <CardTitle>{patternData.name}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium">Pattern Details</h3>
            <div className="mt-2 space-y-2">
              <div>
                <span className="font-medium">Author: </span>
                <span>{patternData.author}</span>
              </div>
              {propsToDisplay.map((key) => {
                const value = patternData[key];
                if (!value || (Array.isArray(value) && value.length === 0))
                  return null;
                return (
                  <div key={key}>
                    <span className="font-medium">{formatKey(key)}: </span>
                    <span>{formatValue(key, value)}</span>
                  </div>
                );
              })}
            </div>
          </div>
          {patternData.tags && patternData.tags.length > 0 && (
            <div>
              <h3 className="text-lg font-medium">Tags</h3>
              <div className="mt-2 flex flex-wrap gap-1">
                {patternData.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
