{"name": "pitterpa<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "type": "module", "engines": {"node": "22.x"}, "scripts": {"build": "next build", "check-pdfjs": "node scripts/check-pdfjs-version.js", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "dev": "next dev --turbopack", "lint": "next lint", "start": "next start"}, "dependencies": {"@ai-sdk/anthropic": "^0.0.50", "@clerk/nextjs": "^6.0.1", "@radix-ui/react-dropdown-menu": "^2.1.10", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@sentry/nextjs": "^8.55.0", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.63.0", "@uploadthing/react": "^7.0.2", "@upstash/ratelimit": "^2.0.1", "@upstash/redis": "^1.34.0", "@vercel/postgres": "^0.9.0", "ai": "^4.1.5", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "drizzle-orm": "^0.30.10", "drizzle-zod": "^0.5.1", "geist": "^1.3.0", "lucide-react": "^0.424.0", "next": "15.1.8", "next-themes": "^0.3.0", "pdfjs-dist": "4.4.168", "postgres": "^3.4.4", "posthog-js": "^1.154.5", "posthog-node": "^4.1.1", "react": "19.1.0", "react-dom": "19.1.0", "react-pdf": "^9.1.0", "server-only": "^0.0.1", "sonner": "^1.5.0", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.1.0", "zod": "^3.23.3", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@types/eslint": "^8.56.10", "@types/node": "^20.14.10", "@types/react": "19.1.5", "@types/react-dom": "19.1.5", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "drizzle-kit": "^0.21.4", "eslint": "^8.57.0", "eslint-config-next": "15.1.8", "eslint-plugin-drizzle": "^0.2.3", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^4.1.8", "typescript": "^5.5.4"}, "ct3aMetadata": {"initVersion": "7.36.2"}, "packageManager": "pnpm@10.11.0", "pnpm": {"overrides": {"@types/react": "19.1.5", "@types/react-dom": "19.1.5"}}}