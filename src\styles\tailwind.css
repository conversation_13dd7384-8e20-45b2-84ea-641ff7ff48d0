@import "tailwindcss";
@import "tailwindcss/utilities";

@theme {
  /* Container */
  --container-padding: 2rem;
  --container-max-width: 1400px;
  
  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* Border radius */
  --radius: 0.5rem;
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-lg: calc(var(--radius) + 2px);
  --radius-xl: calc(var(--radius) + 4px);
  --radius-2xl: calc(var(--radius) + 8px);
  --radius-3xl: calc(var(--radius) + 12px);
  --radius-full: 9999px;

  /* Base colors */
  --color-border: oklch(90% 0.02 250);
  --color-input: oklch(90% 0.02 250);
  --color-ring: oklch(50% 0.2 250);
  --color-background: oklch(100% 0 0);
  --color-foreground: oklch(0% 0 0);
  --color-primary: oklch(50% 0.2 250);
  --color-primary-foreground: oklch(100% 0 0);
  --color-secondary: oklch(96% 0.02 250);
  --color-secondary-foreground: oklch(30% 0.02 250);
  --color-destructive: oklch(60% 0.2 30);
  --color-destructive-foreground: oklch(100% 0 0);
  --color-muted: oklch(96% 0.02 250);
  --color-muted-foreground: oklch(50% 0.02 250);
  --color-accent: oklch(96% 0.02 250);
  --color-accent-foreground: oklch(30% 0.02 250);
  --color-popover: oklch(100% 0 0);
  --color-popover-foreground: oklch(0% 0 0);
  --color-card: oklch(100% 0 0);
  --color-card-foreground: oklch(0% 0 0);

  /* Chart colors */
  --color-chart-1: oklch(0.66 0.19 41.6);
  --color-chart-2: oklch(0.68 0.16 184.9);
  --color-chart-3: oklch(0.48 0.09 210.9);
  --color-chart-4: oklch(0.85 0.19 85.4);
  --color-chart-5: oklch(0.74 0.19 66.3);

  /* Border colors */
  --color-primary-border: oklch(0.59 0.096 111.8);
  --color-destructive-border: oklch(0.43 0.24 29.2);

  /* Sidebar colors */
  --color-sidebar-foreground: oklch(0.41 0.077 78.9);
  --color-sidebar-background: oklch(0.87 0.059 83.7);
  --color-sidebar-primary: oklch(0.26 0.016 0);
  --color-sidebar-primary-foreground: oklch(0.98 0.005 0);
  --color-sidebar-accent: oklch(0.83 0.058 83.6);
  --color-sidebar-accent-foreground: oklch(0.26 0.016 0);
  --color-sidebar-border: oklch(0.91 0.005 0);
  --color-sidebar-ring: oklch(0.71 0.005 0);

  /* Typography */
  --font-weight-light: 700;
  --font-weight-normal: 700;
  --font-weight-medium: 700;
  --font-weight-semibold: 700;
  --font-sans: Nunito, sans-serif;
  --font-serif: PT Serif, serif;

  /* Shadows */
  --shadow-xs: 0 2px 0 0 var(--color-border);
  --shadow-sm: 0 2px 0 0 var(--color-border);
  --shadow-md: 0 2px 0 0 var(--color-border);
  --shadow-lg: 0 2px 0 0 var(--color-border);
  --shadow-xl: 0 2px 0 0 var(--color-border);
  --shadow-2xl: 0 2px 0 0 var(--color-border);
  --shadow-3xl: 0 2px 0 0 var(--color-border);

  /* Spacing */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0.5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1.5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2.5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3.5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-11: 2.75rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-28: 7rem;
  --spacing-32: 8rem;
  --spacing-36: 9rem;
  --spacing-40: 10rem;
  --spacing-44: 11rem;
  --spacing-48: 12rem;
  --spacing-52: 13rem;
  --spacing-56: 14rem;
  --spacing-60: 15rem;
  --spacing-64: 16rem;
  --spacing-72: 18rem;
  --spacing-80: 20rem;
  --spacing-96: 24rem;

  /* Component specific */
  --card-padding: var(--spacing-6);
  --card-border-radius: var(--radius-lg);
  --card-background: var(--color-card);
  --card-foreground: var(--color-card-foreground);
  --card-border: var(--color-border);
  --card-shadow: var(--shadow-sm);

  --badge-padding: var(--spacing-1) var(--spacing-2);
  --badge-border-radius: var(--radius-full);
  --badge-font-size: 0.75rem;
  --badge-font-weight: var(--font-weight-medium);
  --badge-line-height: 1;
  --badge-background: var(--color-secondary);
  --badge-foreground: var(--color-secondary-foreground);
  --badge-border: var(--color-border);

  --input-height: 2.5rem;
  --input-padding: var(--spacing-3);
  --input-border-radius: var(--radius-md);
  --input-background: var(--color-background);
  --input-foreground: var(--color-foreground);
  --input-border: var(--color-border);
  --input-ring: var(--color-ring);

  --select-height: 2.5rem;
  --select-padding: var(--spacing-3);
  --select-border-radius: var(--radius-md);
  --select-background: var(--color-background);
  --select-foreground: var(--color-foreground);
  --select-border: var(--color-border);
  --select-ring: var(--color-ring);

  --label-font-size: 0.875rem;
  --label-font-weight: var(--font-weight-medium);
  --label-line-height: 1.25;
  --label-color: var(--color-foreground);

  /* Transitions */
  --transition-duration-75: 75ms;
  --transition-duration-100: 100ms;
  --transition-duration-150: 150ms;
  --transition-duration-200: 200ms;
  --transition-duration-300: 300ms;
  --transition-duration-500: 500ms;
  --transition-duration-700: 700ms;
  --transition-duration-1000: 1000ms;
  --transition-timing-function-ease: cubic-bezier(0.4, 0, 0.2, 1);
  --transition-timing-function-linear: linear;
  --transition-timing-function-in: cubic-bezier(0.4, 0, 1, 1);
  --transition-timing-function-out: cubic-bezier(0, 0, 0.2, 1);
  --transition-timing-function-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --transition-property-all: all;
  --transition-property-colors: color, background-color, border-color, text-decoration-color, fill, stroke;
  --transition-property-opacity: opacity;
  --transition-property-shadow: box-shadow;
  --transition-property-transform: transform;

  /* Transforms */
  --transform-none: none;
  --transform-gpu: translate3d(0, 0, 0);
  --transform-rotate-0: rotate(0deg);
  --transform-rotate-90: rotate(90deg);
  --transform-rotate-180: rotate(180deg);
  --transform-rotate-270: rotate(270deg);
  --transform-rotate-360: rotate(360deg);
  --transform-scale-0: scale(0);
  --transform-scale-50: scale(0.5);
  --transform-scale-75: scale(0.75);
  --transform-scale-90: scale(0.9);
  --transform-scale-95: scale(0.95);
  --transform-scale-100: scale(1);
  --transform-scale-105: scale(1.05);
  --transform-scale-110: scale(1.1);
  --transform-scale-125: scale(1.25);
  --transform-scale-150: scale(1.5);
  --transform-translate-x-0: translateX(0);
  --transform-translate-y-0: translateY(0);
  --transform-translate-x-1: translateX(0.25rem);
  --transform-translate-y-1: translateY(0.25rem);
  --transform-translate-x-2: translateX(0.5rem);
  --transform-translate-y-2: translateY(0.5rem);
  --transform-translate-x-3: translateX(0.75rem);
  --transform-translate-y-3: translateY(0.75rem);
  --transform-translate-x-4: translateX(1rem);
  --transform-translate-y-4: translateY(1rem);
  --transform-translate-x-5: translateX(1.25rem);
  --transform-translate-y-5: translateY(1.25rem);
  --transform-translate-x-6: translateX(1.5rem);
  --transform-translate-y-6: translateY(1.5rem);
  --transform-translate-x-7: translateX(1.75rem);
  --transform-translate-y-7: translateY(1.75rem);
  --transform-translate-x-8: translateX(2rem);
  --transform-translate-y-8: translateY(2rem);
  --transform-translate-x-9: translateX(2.25rem);
  --transform-translate-y-9: translateY(2.25rem);
  --transform-translate-x-10: translateX(2.5rem);
  --transform-translate-y-10: translateY(2.5rem);
  --transform-translate-x-11: translateX(2.75rem);
  --transform-translate-y-11: translateY(2.75rem);
  --transform-translate-x-12: translateX(3rem);
  --transform-translate-y-12: translateY(3rem);
  --transform-translate-x-14: translateX(3.5rem);
  --transform-translate-y-14: translateY(3.5rem);
  --transform-translate-x-16: translateX(4rem);
  --transform-translate-y-16: translateY(4rem);
  --transform-translate-x-20: translateX(5rem);
  --transform-translate-y-20: translateY(5rem);
  --transform-translate-x-24: translateX(6rem);
  --transform-translate-y-24: translateY(6rem);
  --transform-translate-x-28: translateX(7rem);
  --transform-translate-y-28: translateY(7rem);
  --transform-translate-x-32: translateX(8rem);
  --transform-translate-y-32: translateY(8rem);
  --transform-translate-x-36: translateX(9rem);
  --transform-translate-y-36: translateY(9rem);
  --transform-translate-x-40: translateX(10rem);
  --transform-translate-y-40: translateY(10rem);
  --transform-translate-x-44: translateX(11rem);
  --transform-translate-y-44: translateY(11rem);
  --transform-translate-x-48: translateX(12rem);
  --transform-translate-y-48: translateY(12rem);
  --transform-translate-x-52: translateX(13rem);
  --transform-translate-y-52: translateY(13rem);
  --transform-translate-x-56: translateX(14rem);
  --transform-translate-y-56: translateY(14rem);
  --transform-translate-x-60: translateX(15rem);
  --transform-translate-y-60: translateY(15rem);
  --transform-translate-x-64: translateX(16rem);
  --transform-translate-y-64: translateY(16rem);
  --transform-translate-x-72: translateX(18rem);
  --transform-translate-y-72: translateY(18rem);
  --transform-translate-x-80: translateX(20rem);
  --transform-translate-y-80: translateY(20rem);
  --transform-translate-x-96: translateX(24rem);
  --transform-translate-y-96: translateY(24rem);

  /* PDF Viewer specific */
  --pdf-transition-duration: 700ms;
  --pdf-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
  --pdf-blur-radius: 8px;
  --pdf-skeleton-animation: pulse 2s ease-in-out infinite;
  --pdf-loading-delay: 300ms;
  --pdf-render-delay: 200ms;
}

/* Dark mode colors */
.dark {
  --color-background: oklch(0.18 0.048 83.6);
  --color-foreground: oklch(0.93 0.077 78.9);
  --color-card: oklch(0.18 0.042 83.6);
  --color-card-foreground: oklch(0.93 0.077 74.3);
  --color-popover: oklch(0.18 0.042 83.6);
  --color-popover-foreground: oklch(0.93 0.077 74.3);
  --color-primary: oklch(0.75 0.097 111.7);
  --color-primary-foreground: oklch(0.02 0.005 0);
  --color-secondary: oklch(0.12 0.055 83.6);
  --color-secondary-foreground: oklch(0.92 0.077 78.9);
  --color-muted: oklch(0.14 0.064 83.7);
  --color-muted-foreground: oklch(0.85 0.077 74.3);
  --color-accent: oklch(0.14 0.055 83.6);
  --color-accent-foreground: oklch(0.86 0.016 0);
  --color-destructive: oklch(0.37 0.24 29.2);
  --color-destructive-foreground: oklch(0.03 0.018 0);
  --color-border: oklch(0.12 0.063 80.8);
  --color-input: oklch(0.12 0.063 80.8);
  --color-ring: oklch(0.90 0.077 74.3);
  --color-chart-1: oklch(0.34 0.19 41.6);
  --color-chart-2: oklch(0.32 0.16 184.9);
  --color-chart-3: oklch(0.52 0.09 210.9);
  --color-chart-4: oklch(0.15 0.19 85.4);
  --color-chart-5: oklch(0.26 0.19 66.3);
  --color-primary-border: oklch(0.41 0.096 111.8);
  --color-destructive-border: oklch(0.57 0.24 29.2);
  --color-sidebar-foreground: oklch(0.59 0.077 78.9);
  --color-sidebar-background: oklch(0.13 0.059 83.7);
  --color-sidebar-primary: oklch(0.74 0.016 0);
  --color-sidebar-primary-foreground: oklch(0.02 0.005 0);
  --color-sidebar-accent: oklch(0.17 0.058 83.6);
  --color-sidebar-accent-foreground: oklch(0.74 0.016 0);
  --color-sidebar-border: oklch(0.09 0.005 0);
  --color-sidebar-ring: oklch(0.29 0.005 0);
}

/* Animation keyframes */
@keyframes accordion-down {
  from { height: 0; }
  to { height: var(--radix-accordion-content-height); }
}

@keyframes accordion-up {
  from { height: var(--radix-accordion-content-height); }
  to { height: 0; }
}

@keyframes shimmer {
  from { background-position: 200% 0; }
  to { background-position: -200% 0; }
}

@keyframes pulse {
  50% { opacity: 0.5; }
}

@keyframes enter {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes exit {
  from { opacity: 1; transform: scale(1); }
  to { opacity: 0; transform: scale(0.95); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes zoomIn {
  from { transform: scale(0.95); }
  to { transform: scale(1); }
}

@keyframes zoomOut {
  from { transform: scale(1); }
  to { transform: scale(0.95); }
}

@keyframes slideInFromTop {
  from { transform: translateY(-0.5rem); }
  to { transform: translateY(0); }
}

@keyframes slideInFromBottom {
  from { transform: translateY(0.5rem); }
  to { transform: translateY(0); }
}

@keyframes slideInFromLeft {
  from { transform: translateX(-0.5rem); }
  to { transform: translateX(0); }
}

@keyframes slideInFromRight {
  from { transform: translateX(0.5rem); }
  to { transform: translateX(0); }
} 