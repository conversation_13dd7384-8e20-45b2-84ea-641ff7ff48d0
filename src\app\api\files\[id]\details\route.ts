import { NextResponse } from "next/server";
import { getPatternDetails } from "~/server/queries";
import { auth } from "@clerk/nextjs/server";

export async function GET(
  request: Request,
  { params }: { params: { id: string } },
) {
  try {
    const fileDetails = await getPatternDetails(parseInt(params.id, 10));
    return NextResponse.json(fileDetails);
  } catch (error) {
    if (error instanceof Error && error.message === "Unauthorized") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    console.error("Error fetching file details:", error);
    return NextResponse.json(
      { error: "Failed to fetch file details" },
      { status: 500 },
    );
  }
}
