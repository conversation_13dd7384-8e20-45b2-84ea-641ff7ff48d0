"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  QUERY_KEYS,
  PDF_CACHE_STALE_TIME,
  PDF_CACHE_TIME,
} from "~/lib/constants";
import {
  initPdfCache,
  updatePdfAccessTime,
  storePdfMetadata,
  maintainLRUCache,
  shouldCacheFile,
  removePdfFromCache,
} from "../utils/pdfCacheUtils";

// Cache name is now defined in pdfCacheUtils.ts
const CACHE_NAME = `pdf-documents-v1`;

/**
 * Custom hook for caching PDF documents
 *
 * This hook provides a mechanism to:
 * 1. Check if a PDF document is already cached in React Query
 * 2. Check if a PDF document is cached in the browser's Cache API
 * 3. Fetch the PDF document if not cached
 * 4. Store the fetched PDF document in both React Query and Cache API
 *
 * @param fileUrl - URL of the PDF file to cache
 * @returns Object containing the cached PDF file URL and loading state
 */
export function usePdfDocumentCache(fileUrl: string) {
  const queryClient = useQueryClient();

  // Check if the browser supports Cache API
  const isCacheSupported = typeof caches !== "undefined";

  /**
   * Fetch PDF document from browser's Cache API
   */
  const fetchFromCacheApi = async (url: string): Promise<string | null> => {
    if (!isCacheSupported) return null;

    try {
      const cache = await caches.open(CACHE_NAME);

      // Try to get the cached PDF document
      const response = await cache.match(url);

      if (response) {
        console.log("Using cached PDF document:", url);
        // Update the last accessed time for LRU cache management
        await updatePdfAccessTime(url);
        const blob = await response.blob();
        return URL.createObjectURL(blob);
      }

      return null;
    } catch (error) {
      console.error("Error fetching PDF from cache:", error);
      return null;
    }
  };

  /**
   * Store PDF document in browser's Cache API
   */
  const storeInCacheApi = async (url: string, pdfBlob: Blob): Promise<void> => {
    if (!isCacheSupported) return;

    // Check if the PDF is too large to cache
    if (!shouldCacheFile(pdfBlob.size)) {
      console.log(
        `PDF ${url} is too large (${pdfBlob.size} bytes), not caching`,
      );
      return;
    }

    try {
      const cache = await caches.open(CACHE_NAME);

      // Create a response object from the PDF blob
      const response = new Response(pdfBlob, {
        headers: {
          "Content-Type": "application/pdf",
          "Content-Length": pdfBlob.size.toString(),
          "X-PDF-Cache": "blob",
        },
      });

      // Store the response in the cache
      await cache.put(url, response);

      // Store metadata for cache management
      await storePdfMetadata(url, pdfBlob.size);

      // Maintain the LRU cache (remove oldest entries if needed)
      await maintainLRUCache();

      console.log("Successfully stored PDF document in cache:", url);
    } catch (error) {
      console.error("Error storing PDF document in cache:", error);
    }
  };

  /**
   * Fetch PDF document from URL or cache
   */
  const fetchPdfDocument = async (): Promise<string> => {
    // Initialize the cache if needed
    await initPdfCache();

    // Try to get from browser cache first
    const cachedPdf = await fetchFromCacheApi(fileUrl);
    if (cachedPdf) return cachedPdf;

    // If not in cache, fetch the PDF file
    try {
      const response = await fetch(fileUrl, {
        cache: "no-store", // Ensure we get a fresh response
      });

      if (!response.ok) {
        throw new Error(
          `Failed to fetch PDF: ${response.status} ${response.statusText}`,
        );
      }

      const pdfBlob = await response.blob();

      // Store in browser cache for future use (if it meets size criteria)
      await storeInCacheApi(fileUrl, pdfBlob);

      // Create a local URL for the blob
      return URL.createObjectURL(pdfBlob);
    } catch (error) {
      console.error("Error fetching PDF document:", error);
      throw error;
    }
  };

  // Use React Query to cache the PDF document
  const { data: cachedPdfUrl, isLoading } = useQuery({
    queryKey: QUERY_KEYS.PDF_DOCUMENT(fileUrl),
    queryFn: fetchPdfDocument,
    staleTime: PDF_CACHE_STALE_TIME,
    gcTime: PDF_CACHE_TIME,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  return {
    pdfUrl: cachedPdfUrl,
    isLoading,
  };
}

/**
 * Prefetch a PDF document into the cache
 *
 * @param queryClient - React Query client
 * @param fileUrl - URL of the PDF file to prefetch
 */
export async function prefetchPdfDocument(
  queryClient: ReturnType<typeof useQueryClient>,
  fileUrl: string,
): Promise<void> {
  // If already in the query cache, don't prefetch
  if (queryClient.getQueryData(QUERY_KEYS.PDF_DOCUMENT(fileUrl))) return;

  // Initialize the cache if needed
  await initPdfCache();

  // Prefetch the PDF document
  await queryClient.prefetchQuery({
    queryKey: QUERY_KEYS.PDF_DOCUMENT(fileUrl),
    queryFn: async () => {
      // Check browser cache first
      if (typeof caches !== "undefined") {
        try {
          const cache = await caches.open(CACHE_NAME);
          const response = await cache.match(fileUrl);

          if (response) {
            // Update the last accessed time for LRU cache management
            await updatePdfAccessTime(fileUrl);
            const blob = await response.blob();
            return URL.createObjectURL(blob);
          }

          // If not in cache, fetch and store the PDF
          const fetchResponse = await fetch(fileUrl, {
            cache: "no-store", // Ensure we get a fresh response
          });

          if (!fetchResponse.ok) {
            throw new Error(
              `Failed to prefetch PDF: ${fetchResponse.status} ${fetchResponse.statusText}`,
            );
          }

          const pdfBlob = await fetchResponse.blob();

          // Only cache if it meets our size criteria
          if (!shouldCacheFile(pdfBlob.size)) {
            console.log(
              `PDF ${fileUrl} is too large (${pdfBlob.size} bytes), not caching during prefetch`,
            );
            return URL.createObjectURL(pdfBlob);
          }

          // Create a response object from the PDF blob
          const cacheResponse = new Response(pdfBlob, {
            headers: {
              "Content-Type": "application/pdf",
              "Content-Length": pdfBlob.size.toString(),
              "X-PDF-Cache": "blob",
            },
          });

          // Store the response in the cache
          await cache.put(fileUrl, cacheResponse);

          // Store metadata for cache management
          await storePdfMetadata(fileUrl, pdfBlob.size);

          // Maintain the LRU cache (remove oldest entries if needed)
          await maintainLRUCache();

          console.log(
            "Successfully prefetched PDF document to cache:",
            fileUrl,
          );

          return URL.createObjectURL(pdfBlob);
        } catch (error) {
          console.error("Error prefetching PDF document:", error);
        }
      }

      return fileUrl;
    },
    staleTime: PDF_CACHE_STALE_TIME,
  });
}
