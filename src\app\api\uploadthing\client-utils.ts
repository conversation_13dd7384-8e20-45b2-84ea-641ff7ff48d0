import "client-only";

import { useUploadThing } from "../../utils/uploadthing";
import type { PatternAnalysis } from "~/lib/api/external/ai-file-info/generate-pdf-analysis";
import { saveFileToDb } from "../../actions";
import type { FileInsertData } from "~/server/db/schema";
import type { ClientUploadedFileData } from "uploadthing/types";
import { getAiAnalysis } from "~/lib/api/client/get-ai-analysis";
import { getThumbnail } from "~/lib/pdf-utils";

type Input = Parameters<typeof useUploadThing>;

export type PatternFileData = PatternAnalysis & { fileName: string };

export const useUploadThingInputProps = (...args: Input) => {
  const $ut = useUploadThing(...args);
  // Add thumbnail uploader
  const thumbnailUploader = useUploadThing("thumbnailUploader", {
    onUploadError(error: Error) {
      console.error("Thumbnail upload error:", error);
    },
  });

  const uploadPdfFiles = async (files: FileList) => {
    const selectedFiles = Array.from(files);
    const result = await $ut.startUpload(selectedFiles);
    return result ?? [];
  };

  const uploadThumbnails = async (thumbnails: { thumbnailFile?: File }[]) => {
    const thumbnailFiles = thumbnails
      .filter((t) => t.thumbnailFile)
      .map((t) => t.thumbnailFile!);

    if (!thumbnailFiles.length) {
      throw new Error("No thumbnails were generated for the PDFs");
    }

    const result = await thumbnailUploader.startUpload(thumbnailFiles);
    if (!result?.length) {
      throw new Error("Failed to upload thumbnails");
    }
    return result;
  };

  const combinePdfAndThumbnailData = (
    pdfUploadRes: ClientUploadedFileData<{
      uploadedBy: string;
      url: string;
      fileName: string;
    }>[],
    thumbnailUploadRes: ClientUploadedFileData<{
      uploadedBy: string;
      url: string;
      fileName: string;
    }>[],
  ) => {
    return pdfUploadRes.map((pdfData) => {
      const matchingThumb = thumbnailUploadRes.find(
        (thumbData) => thumbData.name === `thumbnail-${pdfData.name}`,
      );
      if (!matchingThumb) {
        throw new Error(`No matching thumbnail found for PDF: ${pdfData.name}`);
      }
      return {
        ...pdfData,
        thumbnailUrl: matchingThumb.url,
      };
    });
  };

  const onChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;

    const files = e.target.files;
    const thumbnails = await generateThumbnails(files);

    // Check if any thumbnails failed to generate
    const failedThumbnails = thumbnails.filter((t) => !t.thumbnailFile);
    if (failedThumbnails.length > 0) {
      const failedFiles = failedThumbnails.map((t) => t.fileName).join(", ");
      throw new Error(`Failed to generate thumbnails for: ${failedFiles}`);
    }

    const pdfUploadRes = await uploadPdfFiles(files);
    const thumbnailUploadRes = await uploadThumbnails(thumbnails);

    return combinePdfAndThumbnailData(pdfUploadRes, thumbnailUploadRes);
  };

  const multiple = ($ut.routeConfig?.pdf?.maxFileCount ?? 1) > 1;

  return {
    inputProps: {
      onChange,
      multiple,
      accept: "application/pdf", // TODO - use metadata to set this?
    },
    isUploading: $ut.isUploading || thumbnailUploader.isUploading,
  };
};

const getFileDataFromAI = async ({
  file,
}: {
  file: File | undefined;
}): Promise<PatternFileData | undefined> => {
  if (!file) return;
  try {
    const { name: fileName } = file;
    const aiResponse = await getAiAnalysis(file);
    if (!aiResponse) throw Error("AI response is undefined");
    console.log("aiResponse", aiResponse);
    return { ...aiResponse, fileName };
  } catch (err) {
    console.error("Error getting PDF ai data:", err);
  }
};

export const getMultFilesData = async (files: FileList | null) => {
  if (!files?.length) return;
  const fileData = await Promise.all(
    Array.from(files).map(async (file: File) => {
      const data = await getFileDataFromAI({ file });
      return data;
    }),
  );
  if (!fileData?.length) return;
  const filteredData = fileData.filter((data) => data !== undefined);
  return filteredData;
};

// Modify this function to generate thumbnail files that can be uploaded
export const generateThumbnails = async (files: FileList | null) => {
  if (!files?.length) return [];

  const thumbnails = await Promise.all(
    Array.from(files).map(async (file) => {
      try {
        const thumbnailUrl = await getThumbnail(file);

        // Convert data URL to File object for upload
        const response = await fetch(thumbnailUrl);
        const blob = await response.blob();
        const thumbnailFile = new File([blob], `thumbnail-${file.name}`, {
          type: "image/webp",
        });

        return {
          fileName: file.name,
          thumbnailFile,
          thumbnailUrl, // Keep this for local preview if needed
        };
      } catch (err) {
        // Special handling for Promise-related errors
        // This can happen if the PDF.js worker isn't fully loaded when processing starts
        if (
          typeof err === "object" &&
          err !== null &&
          ("then" in err ||
            err instanceof Promise ||
            Object.prototype.toString.call(err) === "[object Promise]")
        ) {
          return {
            fileName: file.name,
            thumbnailFile: undefined,
            thumbnailUrl: undefined,
            error:
              "This PDF file cannot be processed. It may be corrupted or use features not supported by the PDF viewer. Please try a different PDF file.",
          };
        }

        return {
          fileName: file.name,
          thumbnailFile: undefined,
          thumbnailUrl: undefined,
          error: err instanceof Error ? err.message : String(err),
        };
      }
    }),
  );

  return thumbnails;
};

const handleUploadedFile = async ({
  fileData,
  uploadedBy,
  url,
  thumbnailUrl,
}: {
  fileData: PatternAnalysis | undefined;
  uploadedBy: string;
  url: string;
  thumbnailUrl: string;
}) => {
  if (!fileData) return;
  try {
    const fileInsertData: FileInsertData = {
      url,
      userId: uploadedBy,
      thumbnailUrl,
      ...fileData,
    };
    const res = await saveFileToDb(fileInsertData);
    return res;
  } catch (err) {
    console.error("Error saving file to db:", err);
  }
};

export const handleUploadedFiles = async ({
  filesData,
  uploadThingRes,
}: {
  filesData: PatternFileData[] | undefined;
  uploadThingRes: (ClientUploadedFileData<
    | {
        uploadedBy: string;
      }
    | {
        uploadedBy: string;
        url: string;
        fileName: string;
      }
  > & { thumbnailUrl: string })[];
}) => {
  if (!uploadThingRes?.length)
    throw new Error("File upload error: no onChange result");
  if (!filesData?.length) throw new Error("File upload error: no files");
  return await Promise.all(
    uploadThingRes.map(async (uploadData) => {
      const { serverData, url, name, thumbnailUrl } = uploadData;
      if (!("fileName" in serverData))
        throw new Error(`No fileName in serverData for ${name}`);

      const { fileName, uploadedBy } = serverData;
      if (typeof fileName !== "string")
        throw new Error(
          `FileName is not a string for ${name}: ${JSON.stringify(fileName)}`,
        );

      const fileData = filesData?.find(
        (fileData) => fileData.fileName === fileName,
      );

      if (!fileData) throw new Error("File match not found");

      await handleUploadedFile({
        fileData,
        uploadedBy,
        url,
        thumbnailUrl,
      });
    }),
  );
};
