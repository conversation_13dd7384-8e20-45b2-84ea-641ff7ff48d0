import { Skeleton } from "~/components/ui/skeleton";

interface PDFSkeletonProps {
  width: number;
  height: number;
  shouldShowSkeleton: boolean;
}

export const PDFSkeleton: React.FC<PDFSkeletonProps> = ({
  width,
  height,
  shouldShowSkeleton,
}) => (
  <div
    className="absolute inset-0 z-10 transition-opacity"
    style={{
      width: `${width}px`,
      transitionDuration: "var(--pdf-transition-duration)",
      transitionTimingFunction: "var(--pdf-transition-timing)",
      opacity: shouldShowSkeleton ? 1 : 0,
    }}
  >
    <div className="space-y-4 p-4" style={{ width: `${width}px` }}>
      <Skeleton
        className="h-2.5 w-1/4 bg-muted"
        style={{ animation: "var(--pdf-skeleton-animation)" }}
      />
      <Skeleton
        className="h-2 w-1/3 bg-muted"
        style={{ animation: "var(--pdf-skeleton-animation)" }}
      />
      <Skeleton
        className="bg-muted"
        style={{
          height: `${height - 64}px`,
          animation: "var(--pdf-skeleton-animation)",
        }}
      />
      <div className="flex items-center space-x-2">
        <Skeleton
          className="h-6 w-6 rounded-full bg-muted"
          style={{ animation: "var(--pdf-skeleton-animation)" }}
        />
        <Skeleton
          className="h-4 w-16 bg-muted"
          style={{ animation: "var(--pdf-skeleton-animation)" }}
        />
      </div>
    </div>
  </div>
);
