import { auth, clerkClient } from "@clerk/nextjs/server";
import { createUploadthing, type FileRouter } from "uploadthing/next";
import { UploadThingError } from "uploadthing/server";
import { ratelimit } from "~/server/ratelimit";

const f = createUploadthing();

export const FileUploadRouter = {
  pdfUploader: f({ pdf: { maxFileSize: "16MB", maxFileCount: 1 } })
    .middleware(async () => {
      const user = await auth();

      if (!user?.userId) throw new UploadThingError("Unauthorized");

      const clerk = await clerkClient();
      const fullUserData = await clerk.users.getUser(user.userId);

      if (fullUserData?.privateMetadata?.["can-upload"] !== true) {
        throw new UploadThingError("User does not have permission to upload");
      }

      const { success } = await ratelimit.limit(user.userId);
      if (!success) throw new UploadThingError("Ratelimited");

      return {
        userId: user.userId,
      };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // !!! Whatever is returned here is sent to the clientside `onClientUploadComplete` callback
      return {
        uploadedBy: metadata.userId,
        url: file.url,
        fileName: file.name,
      };
    }),
  thumbnailUploader: f({ image: { maxFileSize: "4MB", maxFileCount: 10 } })
    .middleware(async () => {
      const user = await auth();

      if (!user?.userId) throw new UploadThingError("Unauthorized");

      const clerk = await clerkClient();
      const fullUserData = await clerk.users.getUser(user.userId);

      if (fullUserData?.privateMetadata?.["can-upload"] !== true) {
        throw new UploadThingError("User does not have permission to upload");
      }

      const { success } = await ratelimit.limit(user.userId);
      if (!success) throw new UploadThingError("Ratelimited");

      return {
        userId: user.userId,
      };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      return {
        uploadedBy: metadata.userId,
        url: file.url,
        fileName: file.name,
      };
    }),
} satisfies FileRouter;

export type FileUploadRouter = typeof FileUploadRouter;
