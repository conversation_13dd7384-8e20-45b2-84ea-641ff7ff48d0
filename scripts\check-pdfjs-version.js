#!/usr/bin/env node

/**
 * Utility script to check the current pdfjs-dist version used by react-pdf
 * Run with: node scripts/check-pdfjs-version.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

function checkPdfjsVersion() {
  try {
    // Method 1: Check pnpm-lock.yaml
    console.log('🔍 Checking pdfjs-dist version...\n');
    
    const lockFilePath = path.join(path.dirname(__dirname), 'pnpm-lock.yaml');
    if (fs.existsSync(lockFilePath)) {
      const lockContent = fs.readFileSync(lockFilePath, 'utf8');
      const pdfjsMatch = lockContent.match(/pdfjs-dist:\s*(\d+\.\d+\.\d+)/);
      if (pdfjsMatch) {
        console.log(`📦 pdfjs-dist version from pnpm-lock.yaml: ${pdfjsMatch[1]}`);
      }
    }

    // Method 2: Check react-pdf's package.json if available
    const reactPdfPath = path.join(path.dirname(__dirname), 'node_modules', 'react-pdf', 'package.json');
    if (fs.existsSync(reactPdfPath)) {
      const reactPdfPackage = JSON.parse(fs.readFileSync(reactPdfPath, 'utf8'));
      if (reactPdfPackage.dependencies && reactPdfPackage.dependencies['pdfjs-dist']) {
        console.log(`📦 pdfjs-dist version from react-pdf dependencies: ${reactPdfPackage.dependencies['pdfjs-dist']}`);
      }
    }

    // Method 3: Check actual pdfjs-dist package.json
    const pdfjsPath = path.join(path.dirname(__dirname), 'node_modules', 'pdfjs-dist', 'package.json');
    if (fs.existsSync(pdfjsPath)) {
      const pdfjsPackage = JSON.parse(fs.readFileSync(pdfjsPath, 'utf8'));
      console.log(`📦 Installed pdfjs-dist version: ${pdfjsPackage.version}`);
    }

    // Method 4: Check current config file
    const configPath = path.join(path.dirname(__dirname), 'src', 'lib', 'pdfjs-config.ts');
    if (fs.existsSync(configPath)) {
      const configContent = fs.readFileSync(configPath, 'utf8');
      const versionMatch = configContent.match(/return\s+"(\d+\.\d+\.\d+)"/);
      if (versionMatch) {
        console.log(`⚙️  Current fallback version in pdfjs-config.ts: ${versionMatch[1]}`);
      }
    }

    console.log('\n💡 To update the fallback version:');
    console.log('   1. Update the version in src/lib/pdfjs-config.ts');
    console.log('   2. Make sure it matches the pdfjs-dist version used by react-pdf');

  } catch (error) {
    console.error('❌ Error checking pdfjs version:', error.message);
  }
}

checkPdfjsVersion();
