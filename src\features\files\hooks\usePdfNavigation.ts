"use client";

import { useState } from "react";

/**
 * Hook for managing PDF navigation state and controls
 * 
 * Handles page navigation, visibility of navigation controls,
 * and document loading events for PDF viewers.
 * 
 * @returns Navigation state and control functions
 */
export function usePdfNavigation() {
  // Track total number of pages in the document
  const [numPages, setNumPages] = useState<number | null>(null);
  
  // Track current page being displayed
  const [pageNumber, setPageNumber] = useState(1);
  
  // Track visibility of navigation controls
  const [navIsVisible, setNavIsVisible] = useState(false);

  /**
   * Handle successful document load
   * Sets the total page count and resets to first page
   */
  const onDocLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setPageNumber(1);
  };

  /**
   * Navigate to previous page with boundary checking
   * Prevents going below page 1
   */
  const previousPage = () => {
    setPageNumber((prev) => Math.max(prev - 1, 1));
  };

  /**
   * Navigate to next page with boundary checking
   * Prevents going beyond the last page
   */
  const nextPage = () => {
    if (numPages) {
      setPageNumber((prev) => Math.min(prev + 1, numPages));
    }
  };

  /**
   * Show navigation controls when mouse enters the viewer area
   */
  const handleMouseEnter = () => {
    setNavIsVisible(true);
  };

  /**
   * Hide navigation controls when mouse leaves the viewer area
   */
  const handleMouseLeave = () => {
    setNavIsVisible(false);
  };

  return {
    // State
    pageNumber,
    numPages,
    navIsVisible,
    
    // Event handlers
    onDocLoadSuccess,
    previousPage,
    nextPage,
    handleMouseEnter,
    handleMouseLeave,
  };
}
