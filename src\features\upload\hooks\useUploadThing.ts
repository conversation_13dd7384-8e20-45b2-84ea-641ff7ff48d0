"use client";

import type { ChangeEvent } from "react";
import { useUploadThing as useUploadThingBase } from "~/app/utils/uploadthing";
import type { ClientUploadedFileData } from "uploadthing/types";
import { generateThumbnails } from "~/features/files/utils";
import { combinePdfAndThumbnailData } from "../utils";

type Input = Parameters<typeof useUploadThingBase>;

// Response format from UploadThing after successful file processing
type UploadResponse = ClientUploadedFileData<{
  uploadedBy: string;
  url: string;
  fileName: string;
}>[];

type UploadThingInputProps = {
  inputProps: {
    onChange: (e: ChangeEvent<HTMLInputElement>) => Promise<
      | void
      | (ClientUploadedFileData<{
          uploadedBy: string;
          url: string;
          fileName: string;
        }> & { thumbnailUrl: string })[]
    >;
    multiple: boolean;
    accept: string;
  };
  isUploading: boolean;
};

/**
 * Configures a file input for PDF uploads with automatic thumbnail generation
 *
 * Wraps the base UploadThing hook to handle the complete upload flow:
 * generating thumbnails, uploading both PDFs and thumbnails to separate
 * endpoints, and combining the results.
 *
 * @param args - Configuration options for the UploadThing endpoint
 * @returns Props to spread onto an input element and upload state indicators
 */
export const useUploadThingInputProps = (
  ...args: Input
): UploadThingInputProps => {
  const uploadThing = useUploadThingBase(...args);
  // Add thumbnail uploader
  const thumbnailUploader = useUploadThingBase("thumbnailUploader", {
    onUploadError(error: Error) {
      console.error("Thumbnail upload error:", error);
    },
  });

  /**
   * Handles the PDF upload process through UploadThing's API
   * @param files - FileList from input element
   * @returns Processed upload response with server-generated URLs and metadata
   */
  const uploadPdfFiles = async (files: FileList): Promise<UploadResponse> => {
    const selectedFiles = Array.from(files);
    const result = await uploadThing.startUpload(selectedFiles);
    return result ?? [];
  };

  /**
   * Uploads generated thumbnail images to a separate UploadThing endpoint
   * @param thumbnails - Array of thumbnail data objects containing File objects
   * @returns Processed upload response for thumbnails
   */
  const uploadThumbnails = async (
    thumbnails: { thumbnailFile?: File }[],
  ): Promise<UploadResponse> => {
    const thumbnailFiles = thumbnails
      .filter((t) => t.thumbnailFile)
      .map((t) => t.thumbnailFile!);

    if (!thumbnailFiles.length) {
      throw new Error("No thumbnails were generated for the PDFs");
    }

    const result = await thumbnailUploader.startUpload(thumbnailFiles);
    if (!result?.length) {
      throw new Error("Failed to upload thumbnails");
    }
    return result;
  };

  /**
   * Processes file selection, generates thumbnails, and uploads both to separate endpoints
   * @param e - Change event from file input element
   * @returns Array of uploaded file data with thumbnail URLs attached, or void if no files selected
   */
  const onChange = async (
    e: ChangeEvent<HTMLInputElement>,
  ): Promise<
    | void
    | (ClientUploadedFileData<{
        uploadedBy: string;
        url: string;
        fileName: string;
      }> & { thumbnailUrl: string })[]
  > => {
    if (!e.target.files) return;

    const files = e.target.files;
    const thumbnails = await generateThumbnails(files);

    // Check if any thumbnails failed to generate
    const failedThumbnails = thumbnails.filter((t) => !t.thumbnailFile);
    if (failedThumbnails.length > 0) {
      const failedFiles = failedThumbnails.map((t) => t.fileName).join(", ");
      throw new Error(`Failed to generate thumbnails for: ${failedFiles}`);
    }

    const pdfUploadRes = await uploadPdfFiles(files);
    const thumbnailUploadRes = await uploadThumbnails(thumbnails);

    return combinePdfAndThumbnailData(pdfUploadRes, thumbnailUploadRes);
  };

  // Enable multiple file selection only if the route config allows more than one file
  const multiple = (uploadThing.routeConfig?.pdf?.maxFileCount ?? 1) > 1;

  return {
    inputProps: {
      onChange,
      multiple,
      accept: "application/pdf", // TODO: Replace with dynamic mime types from UploadThing metadata
    },
    isUploading: uploadThing.isUploading || thumbnailUploader.isUploading,
  };
};
