"use client";

import { useState, useEffect } from "react";

/**
 * Hook to calculate available height for content based on viewport and offset
 * @param offset Additional offset to subtract from available height (e.g., for padding, margins)
 * @returns The calculated available height in pixels
 */
export function useAvailableHeight(offset: number = 0): number {
  const [availableHeight, setAvailableHeight] = useState(0);

  useEffect(() => {
    // Function to calculate and update the available height
    const updateHeight = () => {
      // Get viewport height
      const viewportHeight = window.innerHeight;

      // Calculate available height by subtracting offset
      setAvailableHeight(viewportHeight - offset);
    };

    // Initial calculation
    updateHeight();

    // Recalculate on window resize
    window.addEventListener("resize", updateHeight);

    // Cleanup
    return () => window.removeEventListener("resize", updateHeight);
  }, [offset]);

  return availableHeight;
}
