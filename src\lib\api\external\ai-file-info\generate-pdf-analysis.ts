"server-only";

import { anthropic } from "@ai-sdk/anthropic";
import { generateObject } from "ai";
import { z } from "zod";

const model = anthropic("claude-3-7-sonnet-latest");

export const patternAnalysisSchema = z.object({
  name: z
    .string()
    .describe(
      "The pattern/project title, usually prominently displayed at the beginning",
    ),
  author: z
    .string()
    .describe(
      "Designer or creator name, typically found near the title, in the copyright section, or header",
    ),
  authorSite: z
    .string()
    .describe(
      "Website, brand, or company that published the pattern (e.g., thesnugglery.net, premieryarns.com)",
    ),
  mainCategory: z
    .string()
    .describe(
      "Primary craft technique: 'Knitting' (look for knit, purl, needles) or 'Crochet' (look for ch, sc, dc, hook)",
    ),
  difficulty: z
    .string()
    .describe(
      "Stated skill level (beginner, easy, intermediate, advanced, complex) or estimate based on techniques used",
    ),
  garmentType: z
    .string()
    .describe(
      "Specific item being created (sweater, cardigan, pullover, hat, shawl, blanket, etc.)",
    ),
  yarnWeight: z
    .string()
    .describe(
      "Weight/thickness of recommended yarn (lace, fingering, sport, DK, worsted, aran, bulky, super bulky) or numerical category (1-7)",
    ),
  ageGroup: z
    .string()
    .describe(
      "Target age group (baby, toddler, child, teen, adult, all ages) based on sizes and styling",
    ),
  seasons: z
    .array(z.string())
    .describe(
      "Appropriate season(s) based on weight and style (Spring, Summer, Fall, Winter, or Year-round for versatile items)",
    ),
  sizes: z
    .array(z.string())
    .describe(
      "All sizes included in the pattern (XS, S, M, L, XL, 2X, 3X, specific measurements, etc.)",
    ),
  tags: z
    .array(z.string())
    .min(2)
    .max(6)
    .describe(
      "2-6 relevant tags briefly describing techniques (cables, colorwork, lace), style (casual, dressy), construction method (top-down, raglan), or distinctive features",
    ),
});

export type PatternAnalysis = z.infer<typeof patternAnalysisSchema>;

export const generatePdfAnalysis = async (
  pdfData: ArrayBuffer | Buffer,
  pdfText: string,
): Promise<PatternAnalysis> => {
  const promptText = `
    Please analyze this yarn craft pattern PDF to extract key information about the project.
    Be specific and use terminology from the craft domain.
    If you're uncertain about any field, make your best estimate based on context clues in the pattern.
    Here is the extracted text from the PDF for backup reference:
    ${pdfText}
  `;

  const { object } = await generateObject({
    model,
    messages: [
      {
        role: "user",
        content: [
          {
            type: "file",
            data: pdfData,
            mimeType: "application/pdf",
          },
          {
            type: "text",
            text: promptText,
          },
        ],
      },
    ],
    schema: patternAnalysisSchema,
    temperature: 0.3,
  });

  return object;
};
