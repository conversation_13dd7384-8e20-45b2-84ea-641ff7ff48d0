"use client";

import { pdfjs } from "react-pdf";

// Use the PDF.js version from react-pdf
// Since we removed the Turbopack alias, this should now work correctly
const PDFJS_VERSION = pdfjs.version;

const PDF_WORKER_SRC = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${PDFJS_VERSION}/pdf.worker.min.mjs`;

let workerPromise: Promise<void> | null = null;
let isWorkerLoaded = false;

/**
 * Loads the PDF.js worker - designed for React Suspense
 * This function will throw a Promise if the worker is not loaded yet,
 * but only on the client side to prevent hydration mismatches
 */
export function loadWorker() {
  // On the server, do nothing and don't throw
  if (typeof window === "undefined") return;

  // If already loaded, nothing to do
  if (isWorkerLoaded) return;

  // Initialize the promise if needed
  if (!workerPromise) {
    console.log("Loading PDF.js worker version:", PDFJS_VERSION);
    workerPromise = Promise.resolve().then(() => {
      if (!pdfjs.GlobalWorkerOptions.workerSrc) {
        pdfjs.GlobalWorkerOptions.workerSrc = PDF_WORKER_SRC;
      }
      isWorkerLoaded = true;
    });
  }

  // Only throw the promise on the client side
  // This prevents hydration mismatches
  throw workerPromise;
}

/**
 * Awaitable version of loadWorker that doesn't throw
 * Use this when you need to ensure the worker is loaded before proceeding
 */
export async function awaitWorkerLoaded(): Promise<void> {
  if (typeof window === "undefined") return;
  if (isWorkerLoaded) return;

  try {
    // Initialize the worker promise if needed
    if (!workerPromise) {
      workerPromise = Promise.resolve().then(() => {
        if (!pdfjs.GlobalWorkerOptions.workerSrc) {
          pdfjs.GlobalWorkerOptions.workerSrc = PDF_WORKER_SRC;
        }
        isWorkerLoaded = true;
      });
    }

    // Wait for the worker to be loaded
    await workerPromise;
  } catch (error) {
    throw error;
  }
}
