"use client";

import { pdfjs } from "react-pdf";

/**
 * Get the PDF.js version to use for the worker
 *
 * When using Turbopack with pdfjs-dist aliased to empty-module.ts,
 * pdfjs.version will be undefined. In this case, we use the version
 * that react-pdf is actually bundled with.
 *
 * To keep this in sync, update this version when upgrading react-pdf:
 * 1. Check pnpm-lock.yaml for the pdfjs-dist version under react-pdf
 * 2. Or check node_modules/react-pdf/node_modules/pdfjs-dist/package.json
 * 3. Update the fallback version below
 */
function getPdfjsVersion(): string {
  if (pdfjs.version) {
    return pdfjs.version;
  }

  // Fallback version - this should match the pdfjs-dist version
  // that react-pdf@9.1.0 uses (as seen in pnpm-lock.yaml)
  return "4.4.168";
}

const PDFJS_VERSION = getPdfjsVersion();

const PDF_WORKER_SRC = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${PDFJS_VERSION}/pdf.worker.min.mjs`;

let workerPromise: Promise<void> | null = null;
let isWorkerLoaded = false;

/**
 * Loads the PDF.js worker - designed for React Suspense
 * This function will throw a Promise if the worker is not loaded yet,
 * but only on the client side to prevent hydration mismatches
 */
export function loadWorker() {
  // On the server, do nothing and don't throw
  if (typeof window === "undefined") return;

  // If already loaded, nothing to do
  if (isWorkerLoaded) return;

  // Initialize the promise if needed
  if (!workerPromise) {
    console.log("Loading worker version:", PDFJS_VERSION);
    console.log("pdfjs.version:", pdfjs.version);
    workerPromise = Promise.resolve().then(() => {
      if (!pdfjs.GlobalWorkerOptions.workerSrc) {
        pdfjs.GlobalWorkerOptions.workerSrc = PDF_WORKER_SRC;
      }
      isWorkerLoaded = true;
    });
  }

  // Only throw the promise on the client side
  // This prevents hydration mismatches
  throw workerPromise;
}

/**
 * Awaitable version of loadWorker that doesn't throw
 * Use this when you need to ensure the worker is loaded before proceeding
 */
export async function awaitWorkerLoaded(): Promise<void> {
  if (typeof window === "undefined") return;
  if (isWorkerLoaded) return;

  try {
    // Initialize the worker promise if needed
    if (!workerPromise) {
      workerPromise = Promise.resolve().then(() => {
        if (!pdfjs.GlobalWorkerOptions.workerSrc) {
          pdfjs.GlobalWorkerOptions.workerSrc = PDF_WORKER_SRC;
        }
        isWorkerLoaded = true;
      });
    }

    // Wait for the worker to be loaded
    await workerPromise;
  } catch (error) {
    throw error;
  }
}
