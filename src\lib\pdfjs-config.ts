"use client";

import { pdfjs } from "react-pdf";

/**
 * Get the PDF.js version to use for the worker
 *
 * Due to Turbopack configuration that aliases pdfjs-dist to empty-module.ts
 * (necessary to prevent SSR issues), pdfjs.version may be undefined.
 * We use a fallback that matches the version react-pdf actually uses.
 */
function getPdfjsVersion(): string {
  if (pdfjs.version) {
    return pdfjs.version;
  }

  // Fallback: This should match the pdfjs-dist version that react-pdf uses
  // Check pnpm-lock.yaml under react-pdf dependencies to verify this version
  return "4.4.168";
}

const PDFJS_VERSION = getPdfjsVersion();

const PDF_WORKER_SRC = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${PDFJS_VERSION}/pdf.worker.min.mjs`;

let workerPromise: Promise<void> | null = null;
let isWorkerLoaded = false;

/**
 * Loads the PDF.js worker - designed for React Suspense
 * This function will throw a Promise if the worker is not loaded yet,
 * but only on the client side to prevent hydration mismatches
 */
export function loadWorker() {
  // On the server, do nothing and don't throw
  if (typeof window === "undefined") return;

  // If already loaded, nothing to do
  if (isWorkerLoaded) return;

  // Initialize the promise if needed
  if (!workerPromise) {
    console.log("Loading PDF.js worker version:", PDFJS_VERSION);
    if (!pdfjs.version) {
      console.log("Note: Using fallback version due to Turbopack alias");
    }
    workerPromise = Promise.resolve().then(() => {
      if (!pdfjs.GlobalWorkerOptions.workerSrc) {
        pdfjs.GlobalWorkerOptions.workerSrc = PDF_WORKER_SRC;
      }
      isWorkerLoaded = true;
    });
  }

  // Only throw the promise on the client side
  // This prevents hydration mismatches
  throw workerPromise;
}

/**
 * Awaitable version of loadWorker that doesn't throw
 * Use this when you need to ensure the worker is loaded before proceeding
 */
export async function awaitWorkerLoaded(): Promise<void> {
  if (typeof window === "undefined") return;
  if (isWorkerLoaded) return;

  try {
    // Initialize the worker promise if needed
    if (!workerPromise) {
      workerPromise = Promise.resolve().then(() => {
        if (!pdfjs.GlobalWorkerOptions.workerSrc) {
          pdfjs.GlobalWorkerOptions.workerSrc = PDF_WORKER_SRC;
        }
        isWorkerLoaded = true;
      });
    }

    // Wait for the worker to be loaded
    await workerPromise;
  } catch (error) {
    throw error;
  }
}
