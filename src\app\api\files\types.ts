import type { NextResponse } from "next/server";
import { z } from "zod";
import {
  basicFileListSchema,
  basicFileSchema,
  patternDetailsSchema,
} from "./schema";

export type BasicFile = z.infer<typeof basicFileSchema>;
export type FileList = z.infer<typeof basicFileListSchema>;
export type PatternDetails = z.infer<typeof patternDetailsSchema>;

export type GetFilesResponse = NextResponse<FileList | { error: string }>;
