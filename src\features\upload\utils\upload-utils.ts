"use client";

import { saveFileToDb } from "~/app/actions";
import type { FileInsertData } from "~/server/db/schema";
import type { ClientUploadedFileData } from "uploadthing/types";
import type { PatternAnalysis } from "~/lib/api/external/ai-file-info/generate-pdf-analysis";
import type { PatternFileData } from "~/features/files/utils";

/**
 * Handle a single uploaded file
 * @param fileData - The pattern data extracted from the file
 * @param uploadedBy - The ID of the user who uploaded the file
 * @param url - The URL of the uploaded file
 * @param thumbnailUrl - The URL of the thumbnail
 * @returns The result of saving the file to the database
 */
export async function handleUploadedFile({
  fileData,
  uploadedBy,
  url,
  thumbnailUrl,
}: {
  fileData: PatternAnalysis | undefined;
  uploadedBy: string;
  url: string;
  thumbnailUrl: string;
}) {
  if (!fileData) return;
  try {
    const fileInsertData: FileInsertData = {
      url,
      userId: uploadedBy,
      thumbnailUrl,
      ...fileData,
    };
    const res = await saveFileToDb(fileInsertData);
    return res;
  } catch (err) {
    console.error("Error saving file to db:", err);
  }
}

/**
 * Handle multiple uploaded files
 * @param filesData - The pattern data extracted from the files
 * @param uploadThingRes - The response from UploadThing
 * @returns The result of saving the files to the database
 */
export async function handleUploadedFiles({
  filesData,
  uploadThingRes,
}: {
  filesData: PatternFileData[] | undefined;
  uploadThingRes: (ClientUploadedFileData<
    | {
        uploadedBy: string;
      }
    | {
        uploadedBy: string;
        url: string;
        fileName: string;
      }
  > & { thumbnailUrl: string })[];
}) {
  if (!uploadThingRes?.length)
    throw new Error("File upload error: no onChange result");
  if (!filesData?.length) throw new Error("File upload error: no files");
  return await Promise.all(
    uploadThingRes.map(async (uploadData) => {
      const { serverData, url, name, thumbnailUrl } = uploadData;
      if (!("fileName" in serverData))
        throw new Error(`No fileName in serverData for ${name}`);

      const { fileName, uploadedBy } = serverData;
      if (typeof fileName !== "string")
        throw new Error(
          `FileName is not a string for ${name}: ${JSON.stringify(fileName)}`,
        );

      const fileData = filesData?.find(
        (fileData) => fileData.fileName === fileName,
      );

      if (!fileData) throw new Error("File match not found");

      await handleUploadedFile({
        fileData,
        uploadedBy,
        url,
        thumbnailUrl,
      });
    }),
  );
}

/**
 * Combine PDF and thumbnail data
 * @param pdfUploadRes - The response from uploading PDFs
 * @param thumbnailUploadRes - The response from uploading thumbnails
 * @returns The combined data
 */
export function combinePdfAndThumbnailData(
  pdfUploadRes: ClientUploadedFileData<{
    uploadedBy: string;
    url: string;
    fileName: string;
  }>[],
  thumbnailUploadRes: ClientUploadedFileData<{
    uploadedBy: string;
    url: string;
    fileName: string;
  }>[],
) {
  return pdfUploadRes.map((pdfData) => {
    const matchingThumb = thumbnailUploadRes.find(
      (thumbData) => thumbData.name === `thumbnail-${pdfData.name}`,
    );
    if (!matchingThumb) {
      throw new Error(`No matching thumbnail found for PDF: ${pdfData.name}`);
    }
    return {
      ...pdfData,
      thumbnailUrl: matchingThumb.url,
    };
  });
}
