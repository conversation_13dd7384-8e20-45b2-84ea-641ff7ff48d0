@import "tailwindcss";
@import "tailwindcss/utilities";
@import "./tailwind.css";
@import "./pdf-overrides.css";

@layer base {
  :root {
    --background: var(--color-background);
    --foreground: var(--color-foreground);
    --card: var(--color-card);
    --card-foreground: var(--color-card-foreground);
    --popover: var(--color-popover);
    --popover-foreground: var(--color-popover-foreground);
    --primary: var(--color-primary);
    --primary-foreground: var(--color-primary-foreground);
    --secondary: var(--color-secondary);
    --secondary-foreground: var(--color-secondary-foreground);
    --muted: var(--color-muted);
    --muted-foreground: var(--color-muted-foreground);
    --accent: var(--color-accent);
    --accent-foreground: var(--color-accent-foreground);
    --destructive: var(--color-destructive);
    --destructive-foreground: var(--color-destructive-foreground);
    --border: var(--color-border);
    --input: var(--color-input);
    --ring: var(--color-ring);
    
    /* Theme toggle variables */
    --sun-transform: scale(1) rotate(0);
    --sun-opacity: 1;
    --moon-transform: scale(0) rotate(90deg);
    --moon-opacity: 0;
  }

  .dark {
    --background: var(--color-background);
    --foreground: var(--color-foreground);
    --card: var(--color-card);
    --card-foreground: var(--color-card-foreground);
    --popover: var(--color-popover);
    --popover-foreground: var(--color-popover-foreground);
    --primary: var(--color-primary);
    --primary-foreground: var(--color-primary-foreground);
    --secondary: var(--color-secondary);
    --secondary-foreground: var(--color-secondary-foreground);
    --muted: var(--color-muted);
    --muted-foreground: var(--color-muted-foreground);
    --accent: var(--color-accent);
    --accent-foreground: var(--color-accent-foreground);
    --destructive: var(--color-destructive);
    --destructive-foreground: var(--color-destructive-foreground);
    --border: var(--color-border);
    --input: var(--color-input);
    --ring: var(--color-ring);
    
    /* Theme toggle variables */
    --sun-transform: scale(0) rotate(-90deg);
    --sun-opacity: 0;
    --moon-transform: scale(1) rotate(0);
    --moon-opacity: 1;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .texture {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
    background-repeat: repeat;
    background-size: 200px 200px;
    opacity: 0.1;
  }

  @keyframes spinner {
    to {
      transform: rotate(360deg);
    }
  }
}

.spinner {
  animation: spinner 1s linear infinite;
}
