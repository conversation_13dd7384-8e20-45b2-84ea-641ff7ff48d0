# PDF.js Setup and Configuration

This document explains how PDF.js is configured in this project and how to maintain it.

## Current Setup

We use `react-pdf` for PDF rendering, which internally uses `pdfjs-dist`. Due to Next.js Turbopack configuration, we have special handling for the PDF.js worker version.

### Configuration Files

- **`src/lib/pdfjs-config.ts`** - Main PDF.js configuration
- **`next.config.js`** - Contains Turbopack aliases that affect PDF.js
- **`scripts/check-pdfjs-version.js`** - Utility to check current versions

### Turbopack Issue

In `next.config.js`, we have:

```javascript
experimental: {
  turbo: {
    resolveAlias: {
      'pdfjs-dist': './empty-module.ts',
    },
  },
}
```

This alias is necessary to prevent SSR issues, but it causes `pdfjs.version` to be `undefined` because the actual `pdfjs-dist` module is replaced with an empty module.

### Solution

Our `pdfjs-config.ts` implements a fallback mechanism:

1. **First**: Try to use `pdfjs.version` if available
2. **Fallback**: Use a hardcoded version that matches the `pdfjs-dist` version used by `react-pdf`

## Maintenance

### When Upgrading react-pdf

1. **Check the new pdfjs-dist version**:
   ```bash
   pnpm run check-pdfjs
   ```

2. **Update the fallback version** in `src/lib/pdfjs-config.ts`:
   ```typescript
   // Update this version to match the new pdfjs-dist version
   return "4.4.168";
   ```

3. **Verify the version** in `pnpm-lock.yaml`:
   ```yaml
   react-pdf@9.1.0:
     dependencies:
       pdfjs-dist: 4.4.168  # This should match your fallback
   ```

### Manual Version Check

You can also manually check versions:

1. **In pnpm-lock.yaml**: Look for `pdfjs-dist: X.X.X` under the `react-pdf` entry
2. **In node_modules**: Check `node_modules/pdfjs-dist/package.json`
3. **Browser console**: The worker loading will log the version being used

## Testing

After updating versions:

1. Start the development server: `pnpm dev`
2. Navigate to a PDF page
3. Check browser console for: `"Loading worker version: X.X.X"`
4. Verify PDFs load and render correctly

## Troubleshooting

### PDF.js Worker Errors

If you see worker-related errors:

1. Check that the fallback version in `pdfjs-config.ts` matches the actual `pdfjs-dist` version
2. Verify the CDN URL is accessible: `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/X.X.X/pdf.worker.min.mjs`
3. Check browser network tab for failed worker requests

### Version Mismatch

If PDFs fail to load after upgrading:

1. Run `pnpm run check-pdfjs` to see current versions
2. Update the fallback version in `pdfjs-config.ts`
3. Clear browser cache and restart dev server

## Alternative Solutions

If this approach becomes problematic, consider:

1. **Remove Turbopack alias**: Remove the `pdfjs-dist` alias and handle SSR issues differently
2. **Use unpkg.com**: Switch to unpkg.com CDN which might be more reliable
3. **Bundle worker locally**: Copy the worker file to public/ and serve it locally
