# PDF.js Setup and Configuration

This document explains how PDF.js is configured in this project.

## Current Setup

We use `react-pdf` for PDF rendering, which internally bundles `pdfjs-dist`. All PDF.js functionality (rendering, thumbnails, text extraction) uses react-pdf's bundled version for consistency.

### Configuration Files

- **`src/lib/pdfjs-config.ts`** - Main PDF.js worker configuration
- **`src/lib/pdf-utils.ts`** - PDF utilities (thumbnails, text extraction)
- **`next.config.js`** - Next.js configuration (only canvas aliased for SSR)

### Unified PDF.js Usage

All PDF.js functionality uses react-pdf's bundled version:

```typescript
// Both files import from react-pdf
import { pdfjs } from "react-pdf";

// Worker configuration
const PDFJS_VERSION = pdfjs.version; // Always available

// PDF operations
const loadingTask = pdfjs.getDocument({ data: arrayBuffer });
```

### Benefits of This Approach

1. **Single source of truth**: One pdfjs-dist version across the entire app
2. **No version mismatches**: Worker and operations use the same version
3. **Simplified configuration**: No complex fallbacks or aliases needed
4. **Better maintenance**: Upgrading react-pdf updates everything

## Maintenance

### When Upgrading react-pdf

1. **Check the new pdfjs-dist version**:

   ```bash
   pnpm run check-pdfjs
   ```

2. **Update the fallback version** in `src/lib/pdfjs-config.ts`:

   ```typescript
   // Update this version to match the new pdfjs-dist version
   return "4.4.168";
   ```

3. **Verify the version** in `pnpm-lock.yaml`:
   ```yaml
   react-pdf@9.1.0:
     dependencies:
       pdfjs-dist: 4.4.168 # This should match your fallback
   ```

### Manual Version Check

You can also manually check versions:

1. **In pnpm-lock.yaml**: Look for `pdfjs-dist: X.X.X` under the `react-pdf` entry
2. **In node_modules**: Check `node_modules/pdfjs-dist/package.json`
3. **Browser console**: The worker loading will log the version being used

## Testing

After updating versions:

1. Start the development server: `pnpm dev`
2. Navigate to a PDF page
3. Check browser console for: `"Loading worker version: X.X.X"`
4. Verify PDFs load and render correctly

## Troubleshooting

### PDF.js Worker Errors

If you see worker-related errors:

1. Check that the fallback version in `pdfjs-config.ts` matches the actual `pdfjs-dist` version
2. Verify the CDN URL is accessible: `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/X.X.X/pdf.worker.min.mjs`
3. Check browser network tab for failed worker requests

### Version Mismatch

If PDFs fail to load after upgrading:

1. Run `pnpm run check-pdfjs` to see current versions
2. Update the fallback version in `pdfjs-config.ts`
3. Clear browser cache and restart dev server

## Alternative Solutions

If this approach becomes problematic, consider:

1. **Remove Turbopack alias**: Remove the `pdfjs-dist` alias and handle SSR issues differently
2. **Use unpkg.com**: Switch to unpkg.com CDN which might be more reliable
3. **Bundle worker locally**: Copy the worker file to public/ and serve it locally
