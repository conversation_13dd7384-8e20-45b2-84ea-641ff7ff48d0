import { NextResponse } from "next/server";
import { generatePdfAnalysis } from "../../../lib/api/external/ai-file-info/generate-pdf-analysis";

const isPDF = (file: File) =>
  file instanceof File && file.type === "application/pdf";

export async function POST(req: Request) {
  try {
    const formData = await req.formData();
    const pdf = formData.get("pdf") as File;
    const pdfText = formData.get("pdfText") as string;

    if (!isPDF(pdf)) {
      return NextResponse.json(
        { error: "Invalid or missing PDF file" },
        { status: 400 },
      );
    }

    if (typeof pdfText !== "string") {
      return NextResponse.json(
        { error: "Invalid or missing PDF text" },
        { status: 400 },
      );
    }

    const pdfData = await pdf.arrayBuffer();

    const analysisRes = await generatePdfAnalysis(pdfData, pdfText);

    return NextResponse.json({ data: analysisRes });
  } catch (error) {
    console.error("Error generating AI info:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
