import { useCallback } from "react";
import {
  useFiles,
  useFile,
  useDeleteFile,
  useDeleteSelectedFiles,
} from "./queries";
import {
  useFilesStoreActions,
  useSelectedFileIds,
  useCurrentFile,
  useSelectedFilesCount,
} from "../store";

export const useFilesActionController = () => {
  const { refetchFiles } = useFiles();
  const {
    toggleFileSelect,
    setSelectedFileIds: setStoreSelectedFileIds,
    setCurrentFile,
    clearSelectedFiles,
  } = useFilesStoreActions();
  const selectedFileIds = useSelectedFileIds();
  const currentFile = useCurrentFile();
  const selectedFilesCount = useSelectedFilesCount();
  const { mutate: deleteFileMutation } = useDeleteFile();
  const { mutate: deleteSelectedFilesMutation } = useDeleteSelectedFiles();

  const fetchFile = useFile;

  const setSelectedFileIds = useCallback(
    (ids: number[]) => {
      setStoreSelectedFileIds(ids);
    },
    [setStoreSelectedFileIds],
  );

  const deleteFile = useCallback(
    (id: number) => {
      deleteFileMutation(id);
      if (selectedFileIds.has(id)) {
        toggleFileSelect(id);
      }
    },
    [deleteFileMutation, selectedFileIds, toggleFileSelect],
  );

  const deleteSelectedFiles = useCallback(() => {
    const idsToDelete = Array.from(selectedFileIds);
    deleteSelectedFilesMutation(idsToDelete);
    clearSelectedFiles();
  }, [deleteSelectedFilesMutation, selectedFileIds, clearSelectedFiles]);

  return {
    refetchFiles,
    fetchFile,
    setSelectedFileIds,
    deleteFile,
    deleteSelectedFiles,
    toggleFileSelect,
    selectedFileIds,
    currentFile,
    setCurrentFile,
    selectedFilesCount,
    clearSelectedFiles,
  };
};
