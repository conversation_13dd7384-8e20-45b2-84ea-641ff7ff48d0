"use client";

import { pdfjs } from "react-pdf";
import { awaitWorkerLoaded } from "./pdfjs-config";

/**
 * Generates a thumbnail from the first page of a PDF file
 * @param file The PDF file to generate a thumbnail from
 * @param options Optional configuration for the thumbnail generation
 * @returns A Promise that resolves to a URL for the thumbnail image
 */
export async function getThumbnail(
  file: File,
  options: {
    scale?: number;
    format?: string;
    quality?: number;
  } = {},
): Promise<string> {
  try {
    // Set default options
    const scale = options.scale ?? 2.5; // Increased scale for better quality thumbnails
    const format = options.format ?? "image/webp";
    const quality = options.quality ?? 0.95; // Increased quality

    // Ensure the worker is properly loaded using the awaitable version
    await awaitWorkerLoaded();

    // Read the file as ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();

    // Load the PDF document
    const loadingTask = pdfjs.getDocument({ data: arrayBuffer });
    const pdf = await loadingTask.promise;

    // Get the first page
    const page = await pdf.getPage(1);

    // Set the scale for a reasonable thumbnail size
    const viewport = page.getViewport({ scale });

    // Create a canvas element
    const canvas = document.createElement("canvas");
    canvas.width = viewport.width;
    canvas.height = viewport.height;
    const context = canvas.getContext("2d");

    if (!context) {
      throw new Error("Could not get canvas context");
    }

    // Render the PDF page to the canvas
    await page.render({
      canvasContext: context,
      viewport: viewport,
    }).promise;

    // Convert canvas to blob
    return new Promise<string>((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (!blob) reject(new Error("Could not create blob from canvas"));
          else resolve(URL.createObjectURL(blob));
        },
        format,
        quality,
      );
    });
  } catch (error) {
    // Throw a descriptive error
    throw new Error(
      `Failed to generate thumbnail for ${file.name}: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * Extracts text content from all pages of a PDF file
 * @param file The PDF file to extract text from
 * @returns A Promise that resolves to the extracted text content
 */
export async function extractPdfText(file: File): Promise<string> {
  try {
    // Ensure the worker is properly loaded
    await awaitWorkerLoaded();

    // Read the file as ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();

    // Load the PDF document
    const loadingTask = pdfjs.getDocument({ data: arrayBuffer });
    const pdf = await loadingTask.promise;

    // Get the total number of pages
    const numPages = pdf.numPages;

    // Extract text from each page
    let fullText = "";

    for (let i = 1; i <= numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();

      // Extract text items and join them with spaces
      const pageText = textContent.items
        .map((item) => {
          // Check if the item has a 'str' property (text item)
          return "str" in item ? (item.str ?? "") : "";
        })
        .join(" ");

      fullText += pageText + "\n\n";
    }

    return fullText.trim();
  } catch (error) {
    // Throw a descriptive error
    throw new Error(
      `Failed to extract text from ${file.name}: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}
