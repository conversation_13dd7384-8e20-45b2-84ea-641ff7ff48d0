"use client";

// Cache version - increment this when the cache structure changes
const CACHE_VERSION = 1;
const CACHE_NAME = `pdf-documents-v${CACHE_VERSION}`;
const MAX_CACHED_ITEMS = 3; // Maximum number of PDFs to cache
const MAX_PDF_SIZE = 5 * 1024 * 1024; // 5MB max size for cached PDFs

interface CacheMetadata {
  url: string;
  size: number;
  created: number;
  lastAccessed: number;
  version: number;
}

/**
 * Check if a PDF should be cached based on its size
 * @param fileSize Size of the PDF file in bytes
 * @returns Boolean indicating if the file should be cached
 */
export function shouldCacheFile(fileSize: number): boolean {
  return fileSize <= MAX_PDF_SIZE;
}

/**
 * Initialize the PDF cache and check for version changes
 */
export async function initPdfCache(): Promise<void> {
  if (typeof caches === "undefined") return;

  try {
    const cache = await caches.open(CACHE_NAME);
    const versionResponse = await cache.match('version');
    
    if (!versionResponse) {
      // First time, just store the version
      await cache.put('version', new Response(CACHE_VERSION.toString()));
      return;
    }
    
    const cachedVersion = await versionResponse.text();
    
    if (cachedVersion !== CACHE_VERSION.toString()) {
      // Version changed, clear cache
      await caches.delete(CACHE_NAME);
      const newCache = await caches.open(CACHE_NAME);
      await newCache.put('version', new Response(CACHE_VERSION.toString()));
      console.log(`PDF cache version changed from ${cachedVersion} to ${CACHE_VERSION}, cache cleared`);
    }
  } catch (error) {
    console.error("Error initializing PDF cache:", error);
  }
}

/**
 * Update the last accessed time for a cached PDF
 * @param url URL of the PDF
 */
export async function updatePdfAccessTime(url: string): Promise<void> {
  if (typeof caches === "undefined") return;

  try {
    const cache = await caches.open(CACHE_NAME);
    const metadataResponse = await cache.match(`${url}-metadata`);
    
    let metadata: CacheMetadata = metadataResponse ? 
      await metadataResponse.json() : 
      { 
        url,
        size: 0,
        created: Date.now(),
        lastAccessed: Date.now(),
        version: CACHE_VERSION
      };
    
    metadata.lastAccessed = Date.now();
    
    await cache.put(
      `${url}-metadata`, 
      new Response(JSON.stringify(metadata), {
        headers: {
          'Content-Type': 'application/json'
        }
      })
    );
  } catch (error) {
    console.error(`Error updating access time for ${url}:`, error);
  }
}

/**
 * Store PDF metadata when caching a PDF
 * @param url URL of the PDF
 * @param size Size of the PDF in bytes
 */
export async function storePdfMetadata(url: string, size: number): Promise<void> {
  if (typeof caches === "undefined") return;

  try {
    const cache = await caches.open(CACHE_NAME);
    const now = Date.now();
    
    const metadata: CacheMetadata = {
      url,
      size,
      created: now,
      lastAccessed: now,
      version: CACHE_VERSION
    };
    
    await cache.put(
      `${url}-metadata`, 
      new Response(JSON.stringify(metadata), {
        headers: {
          'Content-Type': 'application/json'
        }
      })
    );
  } catch (error) {
    console.error(`Error storing metadata for ${url}:`, error);
  }
}

/**
 * Maintain the LRU cache by removing oldest entries beyond our limit
 */
export async function maintainLRUCache(): Promise<void> {
  if (typeof caches === "undefined") return;

  try {
    const cache = await caches.open(CACHE_NAME);
    const requests = await cache.keys();
    
    // Filter out the version entry and metadata entries
    const pdfRequests = requests.filter(request => 
      !request.url.endsWith('-metadata') && 
      !request.url.endsWith('/version')
    );
    
    if (pdfRequests.length <= MAX_CACHED_ITEMS) {
      return; // No need to prune if we're under the limit
    }
    
    // Get metadata for all cached PDFs
    const entries: { url: string; metadata: CacheMetadata }[] = [];
    
    for (const request of pdfRequests) {
      const metadataResponse = await cache.match(`${request.url}-metadata`);
      if (metadataResponse) {
        try {
          const metadata = await metadataResponse.json();
          entries.push({ url: request.url, metadata });
        } catch (error) {
          console.error(`Error parsing metadata for ${request.url}:`, error);
        }
      } else {
        // If no metadata, use a default with old timestamp to prioritize removal
        entries.push({ 
          url: request.url, 
          metadata: { 
            url: request.url,
            size: 0,
            created: 0,
            lastAccessed: 0,
            version: CACHE_VERSION
          } 
        });
      }
    }
    
    // Sort by last accessed time (most recent first)
    entries.sort((a, b) => b.metadata.lastAccessed - a.metadata.lastAccessed);
    
    // Remove oldest entries beyond our limit
    for (let i = MAX_CACHED_ITEMS; i < entries.length; i++) {
      await cache.delete(entries[i].url);
      await cache.delete(`${entries[i].url}-metadata`);
      console.log(`Removed old PDF from cache: ${entries[i].url}`);
    }
  } catch (error) {
    console.error("Error maintaining LRU cache:", error);
  }
}

/**
 * Clear all cached PDFs
 */
export async function clearPdfCache(): Promise<void> {
  if (typeof caches === "undefined") return;
  
  try {
    await caches.delete(CACHE_NAME);
    console.log("PDF cache cleared successfully");
  } catch (error) {
    console.error("Error clearing PDF cache:", error);
  }
}

/**
 * Remove a specific PDF from cache
 * @param url URL of the PDF to remove
 */
export async function removePdfFromCache(url: string): Promise<void> {
  if (typeof caches === "undefined") return;
  
  try {
    const cache = await caches.open(CACHE_NAME);
    const success = await cache.delete(url);
    await cache.delete(`${url}-metadata`);
    console.log(`PDF ${url} ${success ? "removed from" : "not found in"} cache`);
  } catch (error) {
    console.error(`Error removing PDF ${url} from cache:`, error);
  }
}

/**
 * Get cache size information
 * @returns Cache statistics or null if error
 */
export async function getPdfCacheStats(): Promise<{
  count: number;
  totalSize: number;
  formattedSize: string;
  items: { url: string; size: number; lastAccessed: number }[];
} | null> {
  if (typeof caches === "undefined") return null;
  
  try {
    const cache = await caches.open(CACHE_NAME);
    const requests = await cache.keys();
    
    // Filter out the version entry and metadata entries
    const pdfRequests = requests.filter(request => 
      !request.url.endsWith('-metadata') && 
      !request.url.endsWith('/version')
    );
    
    let totalSize = 0;
    const items: { url: string; size: number; lastAccessed: number }[] = [];
    
    for (const request of pdfRequests) {
      const response = await cache.match(request);
      const metadataResponse = await cache.match(`${request.url}-metadata`);
      
      let size = 0;
      let lastAccessed = 0;
      
      if (response) {
        const blob = await response.blob();
        size = blob.size;
      }
      
      if (metadataResponse) {
        try {
          const metadata = await metadataResponse.json();
          lastAccessed = metadata.lastAccessed || 0;
          // Use metadata size if available and response size is not
          if (metadata.size && !size) {
            size = metadata.size;
          }
        } catch (error) {
          console.error(`Error parsing metadata for ${request.url}:`, error);
        }
      }
      
      totalSize += size;
      items.push({ 
        url: request.url, 
        size, 
        lastAccessed 
      });
    }
    
    // Sort by last accessed time (most recent first)
    items.sort((a, b) => b.lastAccessed - a.lastAccessed);
    
    return {
      count: pdfRequests.length,
      totalSize,
      formattedSize: formatBytes(totalSize),
      items
    };
  } catch (error) {
    console.error("Error getting PDF cache stats:", error);
    return null;
  }
}

/**
 * Format bytes to human-readable format
 * @param bytes Number of bytes
 * @returns Formatted string
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
