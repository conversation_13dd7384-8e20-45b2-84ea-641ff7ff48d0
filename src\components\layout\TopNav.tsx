import { SignedIn, SignedOut, SignIn<PERSON><PERSON>on, <PERSON>r<PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import Link from "next/link";
import { PdfUploadButton } from "~/features/upload/components";
import { ThemeToggle } from "~/components/ThemeToggle";

export function TopNav() {
  return (
    <nav className="flex items-center justify-between border-b p-4 text-xl font-semibold">
      <Link href="/">
        <div>Gallery</div>
      </Link>

      <div className="flex flex-row items-center gap-4">
        <SignedOut>
          <SignInButton />
        </SignedOut>
        <SignedIn>
          <ThemeToggle />
          <PdfUploadButton />
          <UserButton />
        </SignedIn>
      </div>
    </nav>
  );
}
