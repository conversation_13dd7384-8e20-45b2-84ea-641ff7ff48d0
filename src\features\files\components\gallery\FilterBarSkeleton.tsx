import React from "react";
import { Skeleton } from "~/components/ui/skeleton";

export const FilterBarSkeleton: React.FC = () => {
  return (
    <div className="mb-4 flex flex-wrap items-center gap-4 rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
      {/* Search input skeleton */}
      <div className="flex w-full flex-col gap-1.5 sm:w-64">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Category filter skeletons */}
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="flex w-full flex-col gap-1.5 sm:w-40">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-10 w-full" />
        </div>
      ))}

      {/* Sort dropdown skeleton */}
      <div className="flex w-full flex-col gap-1.5 sm:w-40">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  );
};
