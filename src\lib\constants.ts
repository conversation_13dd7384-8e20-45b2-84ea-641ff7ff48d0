export const REACT_QUERY_STALE_TIME = 1000 * 60 * 5; // 5 minutes
export const PDF_CACHE_STALE_TIME = 1000 * 60 * 60 * 24; // 24 hours - PDFs rarely change
export const PDF_CACHE_TIME = 1000 * 60 * 60 * 24 * 7; // 7 days

export const QUERY_KEYS = {
  FILES: ["files"] as const,
  FILE: (id: number) => ["file", id] as const,
  PDF_DOCUMENT: (fileUrl: string) => ["pdf-document", fileUrl] as const,
} as const;

export const TANSTACK_QUERY__FILES_CONFIG = {
  queryKey: QUERY_KEYS.FILES,
  staleTime: REACT_QUERY_STALE_TIME, // 5 minutes
  refetchOnMount: false,
  refetchOnWindowFocus: false,
};
