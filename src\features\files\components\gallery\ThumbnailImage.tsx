"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Loader2 } from "lucide-react";

interface ThumbnailImageProps {
  src: string | null;
  alt?: string;
  width?: number;
  height?: number;
  className?: string;
  onLoad?: () => void;
}

const DEFAULT_DIMENSIONS = {
  width: 192,
  height: 256,
} as const;

export const ThumbnailImage: React.FC<ThumbnailImageProps> = ({
  src,
  alt = "PDF thumbnail",
  width = DEFAULT_DIMENSIONS.width,
  height = DEFAULT_DIMENSIONS.height,
  className = "",
  onLoad,
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);

  if (!src) return null;

  return (
    <div className="relative h-full w-full">
      {/* Loading skeleton that fades out when image loads */}
      <div
        className={`absolute inset-0 bg-card transition-opacity duration-500 ease-in-out ${
          imageLoaded ? "opacity-0" : "opacity-100"
        }`}
        style={{ zIndex: imageLoaded ? -1 : 1 }}
      >
        <div className="relative h-full w-full overflow-hidden rounded-lg">
          {/* Base background */}
          <div className="bg-primary/10 absolute inset-0"></div>

          {/* Animated background with shimmer effect */}
          <div
            className="from-primary/5 via-primary/30 to-primary/5 absolute inset-0 animate-shimmer bg-gradient-to-r"
            style={{ backgroundSize: "200% 100%" }}
          />

          {/* Pulsing overlay */}
          <div className="bg-primary/10 absolute inset-0 animate-pulse"></div>

          {/* Loading indicator */}
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader2 className="text-primary/50 h-6 w-6 animate-spin" />
          </div>
        </div>
      </div>

      {/* Actual image */}
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={`h-full w-full object-cover transition-opacity duration-500 ease-in-out ${
          imageLoaded ? "opacity-100" : "opacity-0"
        } ${className}`}
        style={{
          maxHeight: "100%",
          maxWidth: "100%",
          objectPosition: "left top", // Position image at top-left corner
        }}
        onLoad={() => {
          setImageLoaded(true);
          if (onLoad) onLoad();
        }}
      />
    </div>
  );
};
