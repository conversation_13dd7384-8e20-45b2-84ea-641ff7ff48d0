import "~/styles/globals.css";
import "@uploadthing/react/styles.css";

//import { GeistSans } from "geist/font/sans";
import type { Metadata } from "next";
import { ClerkProvider } from "@clerk/nextjs";
import { TopNav } from "~/components/layout/TopNav";
import { NextSSRPlugin } from "@uploadthing/react/next-ssr-plugin";
import { extractRouterConfig } from "uploadthing/server";
import { FileUploadRouter } from "./api/uploadthing/core";
import { Toaster } from "sonner";
import { PostHogProviderWrapper as PostHogProvider } from "./_analytics/provider";
import { Nunito } from "next/font/google";
import { PT_Sans } from "next/font/google";
import "../lib/promise-polyfill";
import QueryProvider from "~/providers/QueryProvider";
import ErrorBoundary from "~/components/ErrorBoundary";
import { ThemeProvider } from "~/providers/ThemeProviderWrapper";

export const metadata: Metadata = {
  title: "Pitter Pattern",
  description: "Generated by create-t3-app",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const nunito = Nunito({
  variable: "--font-nunito",
  subsets: ["latin"],
});

const ptSans = PT_Sans({
  variable: "--font-pt-sans",
  subsets: ["latin"],
  weight: ["400", "700"],
});

export default function RootLayout({
  children,
  modal,
}: Readonly<{ children: React.ReactNode; modal: React.ReactNode }>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${nunito.variable} ${ptSans.variable} relative antialiased`}
      >
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <ClerkProvider dynamic>
            <PostHogProvider>
              <NextSSRPlugin
                routerConfig={extractRouterConfig(FileUploadRouter)}
              />
              <QueryProvider>
                <ErrorBoundary
                  fallback={
                    <div>
                      Something went wrong. Please try refreshing the page.
                    </div>
                  }
                >
                  <div className="texture" />
                  <div className="grid h-screen grid-rows-[auto,1fr]">
                    <TopNav />
                    <main className="overflow-auto">{children}</main>
                    {modal}
                  </div>
                  <div id="modal-root" />
                  <Toaster />
                </ErrorBoundary>
              </QueryProvider>
            </PostHogProvider>
          </ClerkProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
