"use client";

import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { usePostHog } from "posthog-js/react";
import { useUploadThingInputProps } from "~/features/upload/hooks";
import { handleUploadedFiles } from "~/features/upload/utils";
import { getMultFilesData, type PatternFileData } from "~/features/files/utils";
import { useFilesActionController } from "~/features/files/hooks/controller";
import type { ClientUploadedFileData } from "uploadthing/types";
import { Button } from "~/components/ui/button";

/**
 * Upload icon SVG component
 */
function UploadSVG() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth={1.5}
      stroke="currentColor"
      className="size-6"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
      />
    </svg>
  );
}

/**
 * Animated loading spinner SVG component
 */
function LoadingSpinnerSVG() {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      className="animate-spin"
    >
      <path
        d="M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z"
        opacity=".25"
      />
      <path d="M10.14,1.16a11,11,0,0,0-9,8.92A1.59,1.59,0,0,0,2.46,12,1.52,1.52,0,0,0,4.11,10.7a8,8,0,0,1,6.66-6.61A1.42,1.42,0,0,0,12,2.69h0A1.57,1.57,0,0,0,10.14,1.16Z" />
    </svg>
  );
}

/**
 * Loading spinner with text for toast notifications
 */
function LoadingSpinner() {
  return (
    <div className="flex items-center gap-2 text-foreground">
      <LoadingSpinnerSVG /> <span className="text-l">Uploading...</span>
    </div>
  );
}

/**
 * PDF upload button component that handles the complete upload flow:
 * 1. Allows users to select PDF files
 * 2. Extracts pattern data from the PDFs
 * 3. Uploads the PDFs and their thumbnails
 * 4. Saves the file data to the database
 * 5. Provides visual feedback during the upload process
 */
export function PdfUploadButton() {
  const router = useRouter();
  const posthog = usePostHog();
  const { refetchFiles } = useFilesActionController();

  /**
   * Extract pattern data from selected PDF files
   * @param e - The file input change event
   * @returns The extracted pattern data
   */
  const saveFilesData = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    const filesData = await getMultFilesData(files);
    return filesData;
  };

  /**
   * Process uploaded files by saving them to the database and updating the UI
   * @param filesData - The pattern data extracted from the PDFs
   * @param uploadResult - The result from UploadThing containing file URLs
   */
  const processUpload = async (
    filesData: PatternFileData[] | undefined,
    uploadResult: (ClientUploadedFileData<{
      uploadedBy: string;
      url: string;
      fileName: string;
    }> & { thumbnailUrl: string })[],
  ) => {
    await handleUploadedFiles({
      filesData,
      uploadThingRes: uploadResult,
    });
    toast.dismiss("upload-begin");
    toast("Upload complete!");
    await refetchFiles();
    router.refresh();
  };

  const { inputProps, isUploading } = useUploadThingInputProps("pdfUploader", {
    onUploadBegin() {
      console.log("onUploadBegin");
      posthog.capture("upload-begin");
      toast(<LoadingSpinner />, {
        duration: 100000,
        id: "upload-begin",
      });
    },
    onUploadError(error: Error) {
      posthog.capture("upload-error", { error });
      toast.dismiss("upload-begin");
      toast("Upload failed");
    },
  });

  const { onChange: onChangeUploadThing } = inputProps;

  /**
   * Handle file input change event with the complete upload flow
   * @param e - The file input change event
   */
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    if (!files?.length) return;

    // First get the file data
    saveFilesData(e)
      .then(async (filesData) => {
        // Then handle the upload
        const fileDataPlusThumbnail = await onChangeUploadThing(e);
        if (!fileDataPlusThumbnail?.length) {
          throw new Error("File upload error, no onChange result");
        }
        // Finally process the upload
        await processUpload(filesData, fileDataPlusThumbnail);
      })
      .catch((error: Error) => {
        posthog.capture("upload-file-error", { message: error.message });
        console.error("Error in onChange:", error);

        // Dismiss the loading toast
        toast.dismiss("upload-begin");
        toast.error("Upload failed");
      });
  };

  return (
    <div>
      <Button
        variant="default"
        className="relative"
        disabled={isUploading}
        asChild
      >
        <label
          htmlFor="upload-button"
          className="flex cursor-pointer items-center gap-2"
        >
          {isUploading ? (
            <>
              <LoadingSpinnerSVG />
              <span>Uploading...</span>
            </>
          ) : (
            <>
              <UploadSVG />
              <span>Upload PDF</span>
            </>
          )}
        </label>
      </Button>
      <input
        id="upload-button"
        type="file"
        className="sr-only"
        {...{ ...inputProps, onChange }}
        multiple
        disabled={isUploading}
      />
    </div>
  );
}
