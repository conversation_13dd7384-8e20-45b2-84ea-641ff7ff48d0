"use client";

import { getThumbnail } from "~/lib/pdf-utils";
import { getAiAnalysis } from "~/lib/api/client/get-ai-analysis";
import type { PatternAnalysis } from "~/lib/api/external/ai-file-info/generate-pdf-analysis";

export type PatternFileData = PatternAnalysis & { fileName: string };

/**
 * Generate thumbnail files from PDF files
 * @param files - The PDF files to generate thumbnails for
 * @returns An array of objects containing the file name, thumbnail file, and thumbnail URL
 */
export async function generateThumbnails(files: FileList | null) {
  if (!files?.length) return [];

  const thumbnails = await Promise.all(
    Array.from(files).map(async (file) => {
      try {
        const thumbnailUrl = await getThumbnail(file);

        // Convert data URL to File object for upload
        const response = await fetch(thumbnailUrl);
        const blob = await response.blob();
        const thumbnailFile = new File([blob], `thumbnail-${file.name}`, {
          type: "image/webp",
        });

        return {
          fileName: file.name,
          thumbnailFile,
          thumbnailUrl, // Keep this for local preview if needed
        };
      } catch (err) {
        // Special handling for Promise-related errors
        // This can happen if the PDF.js worker isn't fully loaded when processing starts
        if (
          typeof err === "object" &&
          err !== null &&
          ("then" in err ||
            err instanceof Promise ||
            Object.prototype.toString.call(err) === "[object Promise]")
        ) {
          return {
            fileName: file.name,
            thumbnailFile: undefined,
            thumbnailUrl: undefined,
            error:
              "This PDF file cannot be processed. It may be corrupted or use features not supported by the PDF viewer. Please try a different PDF file.",
          };
        }

        return {
          fileName: file.name,
          thumbnailFile: undefined,
          thumbnailUrl: undefined,
          error: err instanceof Error ? err.message : String(err),
        };
      }
    }),
  );

  return thumbnails;
}

/**
 * Get pattern data from a PDF file using AI analysis
 * @param file - The PDF file to analyze
 * @returns The pattern data extracted from the PDF
 */
export async function getFileDataFromAI({
  file,
}: {
  file: File | undefined;
}): Promise<PatternFileData | undefined> {
  if (!file) return;
  try {
    const { name: fileName } = file;
    const aiResponse = await getAiAnalysis(file);
    if (!aiResponse) throw Error("AI response is undefined");
    console.log("aiResponse", aiResponse);
    return { ...aiResponse, fileName };
  } catch (err) {
    console.error("Error getting PDF ai data:", err);
  }
}

/**
 * Get pattern data from multiple PDF files
 * @param files - The PDF files to analyze
 * @returns An array of pattern data extracted from the PDFs
 */
export async function getMultFilesData(files: FileList | null) {
  if (!files?.length) return;
  const fileData = await Promise.all(
    Array.from(files).map(async (file: File) => {
      const data = await getFileDataFromAI({ file });
      return data;
    }),
  );
  if (!fileData?.length) return;
  const filteredData = fileData.filter((data) => data !== undefined);
  return filteredData as PatternFileData[];
}
