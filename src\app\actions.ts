"use server";

import { db } from "~/server/db";
import {
  pdfs,
  seasons,
  sizes,
  tags,
  pdfSeasons,
  pdfSizes,
  pdfTags,
  InsertPdfSchema,
} from "~/server/db/schema";
import type { FileInsertData } from "~/server/db/schema";
import { eq } from "drizzle-orm";

export async function saveFileToDb(file: FileInsertData) {
  try {
    const validatedData = InsertPdfSchema.parse(file);

    // Start a transaction for atomic operation
    return await db.transaction(async (tx) => {
      // Insert main PDF record
      const [pdf] = await tx
        .insert(pdfs)
        .values({
          name: validatedData.name,
          url: validatedData.url,
          userId: validatedData.userId,
          author: validatedData.author,
          authorSite: validatedData.authorSite,
          mainCategory: validatedData.mainCategory,
          difficulty: validatedData.difficulty,
          garmentType: validatedData.garmentType,
          yarnWeight: validatedData.yarnWeight,
          ageGroup: validatedData.ageGroup,
          thumbnailUrl: validatedData.thumbnailUrl,
        })
        .returning({ id: pdfs.id });

      if (!pdf)
        throw new Error(`Failed to insert pattern: ${validatedData.url}`);

      // Handle seasons
      for (const seasonName of validatedData.seasons) {
        const [season] = await tx
          .insert(seasons)
          .values({ name: seasonName })
          .onConflictDoNothing()
          .returning({ id: seasons.id });

        // If insert succeeded, use that ID. Only query if needed
        const seasonId = season
          ? season?.id
          : (
              await tx
                .select({ id: seasons.id })
                .from(seasons)
                .where(eq(seasons.name, seasonName))
            )[0]?.id;

        if (!seasonId)
          throw new Error(`Failed to get/create season: ${seasonName}`);

        await tx.insert(pdfSeasons).values({ pdfId: pdf.id, seasonId });
      }

      // Handle sizes
      for (const sizeName of validatedData.sizes) {
        const [size] = await tx
          .insert(sizes)
          .values({ name: sizeName })
          .onConflictDoNothing()
          .returning({ id: sizes.id });

        const sizeId =
          size?.id ||
          (
            await tx
              .select({ id: sizes.id })
              .from(sizes)
              .where(eq(sizes.name, sizeName))
          )[0]?.id;

        if (!sizeId) throw new Error(`Failed to get/create size: ${sizeName}`);

        await tx.insert(pdfSizes).values({ pdfId: pdf.id, sizeId });
      }

      // Handle tags
      for (const tagName of validatedData.tags) {
        const [tag] = await tx
          .insert(tags)
          .values({ name: tagName })
          .onConflictDoNothing()
          .returning({ id: tags.id });

        const tagId =
          tag?.id ||
          (
            await tx
              .select({ id: tags.id })
              .from(tags)
              .where(eq(tags.name, tagName))
          )[0]?.id;

        if (!tagId) throw new Error(`Failed to get/create tag: ${tagName}`);

        await tx.insert(pdfTags).values({ pdfId: pdf.id, tagId });
      }

      return pdf.id;
    });
  } catch (error) {
    console.error("Error saving file to database:", error);
    throw error;
  }
}
