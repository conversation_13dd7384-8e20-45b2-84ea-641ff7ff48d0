import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { deleteFile, fetchFile, fetchFiles } from "../api";
import { TANSTACK_QUERY__FILES_CONFIG, QUERY_KEYS } from "~/lib/constants";
import type { BasicFile } from "~/app/api/files/types";

export const useFiles = () => {
  const queryClient = useQueryClient();

  const { data, isLoading } = useQuery({
    ...{ queryFn: fetchFiles },
    ...TANSTACK_QUERY__FILES_CONFIG,
  });

  const refetchFiles = async () => {
    await queryClient.invalidateQueries({ queryKey: QUERY_KEYS.FILES });
  };

  return { files: data, refetchFiles, isLoading };
};

export const useFile = (id: number) => {
  return useQuery({
    queryKey: QUERY_KEYS.FILE(id),
    queryFn: () => fetchFile(id),
    staleTime: 1000 * 60 * 60, // 1 hour
  });
};

export const useDeleteFile = () => {
  const queryClient = useQueryClient();

  return useMutation<void, Error, number>({
    mutationFn: deleteFile,
    onSuccess: (_, deletedFileId) => {
      queryClient.setQueryData<BasicFile[]>(QUERY_KEYS.FILES, (oldFiles) => {
        return oldFiles
          ? oldFiles.filter((file) => file.id !== deletedFileId)
          : [];
      });
      queryClient.removeQueries({ queryKey: QUERY_KEYS.FILE(deletedFileId) });
    },
  });
};

export const useDeleteSelectedFiles = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (fileIds: number[]) => Promise.all(fileIds.map(deleteFile)),
    onSuccess: (_, deletedFileIds) => {
      queryClient.setQueryData<BasicFile[]>(QUERY_KEYS.FILES, (oldFiles) => {
        return oldFiles
          ? oldFiles.filter((file) => !deletedFileIds.includes(file.id))
          : [];
      });
      deletedFileIds.forEach((id) =>
        queryClient.removeQueries({ queryKey: QUERY_KEYS.FILE(id) }),
      );
    },
  });
};
