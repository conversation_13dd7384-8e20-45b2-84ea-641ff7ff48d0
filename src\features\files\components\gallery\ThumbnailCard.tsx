"use client";

import React, { useState, useCallback } from "react";
import { Card } from "~/components/ui/card";
import { ThumbnailImage } from "./ThumbnailImage";
import { prefetchPdfDocument } from "../../hooks";
import { useQueryClient } from "@tanstack/react-query";

interface ThumbnailCardProps {
  src: string | null;
  name?: string;
  author?: string;
  category?: string;
  difficulty?: string;
  showLabels?: boolean;
  width?: number;
  height?: number;
  className?: string;
  pdfUrl?: string; // URL to the PDF file for prefetching
}

const DEFAULT_DIMENSIONS = {
  width: 192,
  height: 256,
} as const;

export const ThumbnailCard: React.FC<ThumbnailCardProps> = ({
  src,
  name,
  author,
  category,
  difficulty,
  showLabels = false,
  width = DEFAULT_DIMENSIONS.width,
  height = DEFAULT_DIMENSIONS.height,
  className = "",
  pdfUrl,
}) => {
  const [showContent, setShowContent] = useState(false);
  const queryClient = useQueryClient();

  // Prefetch PDF on hover
  const handleMouseEnter = useCallback(async () => {
    if (pdfUrl) {
      await prefetchPdfDocument(queryClient, pdfUrl);
    }
  }, [pdfUrl, queryClient]);

  if (!src) return null;

  return (
    <Card
      className={`relative overflow-hidden p-0 ${className}`}
      onMouseEnter={handleMouseEnter}
    >
      <ThumbnailImage
        src={src}
        alt={name ?? "PDF thumbnail"}
        width={width}
        height={height}
        onLoad={() => {
          // Delay showing content for a smoother transition
          setTimeout(() => setShowContent(true), 200);
        }}
      />

      {/* Content overlay that fades in after image loads */}
      {showLabels && (
        <div
          className={`absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/70 to-transparent p-3 text-white transition-opacity duration-500 ease-in-out ${
            showContent ? "opacity-100" : "opacity-0"
          }`}
        >
          {name && <div className="truncate text-sm font-medium">{name}</div>}
          {author && (
            <div className="truncate text-xs opacity-90">{author}</div>
          )}
          {category && difficulty && (
            <div className="truncate text-xs opacity-90">
              {category} - {difficulty}
            </div>
          )}
        </div>
      )}
    </Card>
  );
};
