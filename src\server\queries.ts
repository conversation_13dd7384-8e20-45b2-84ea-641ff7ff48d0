import "server-only";
import { db } from "./db";
import { and, eq } from "drizzle-orm";
import { pdfs } from "./db/schema";
import { analyticsServerClient } from "./analytics";
import { auth } from "@clerk/nextjs/server";

export async function getUserId() {
  const { userId } = await auth();
  if (!userId) throw new Error("Unauthorized");
  return userId;
}

// PDF Files

export async function getUserFiles() {
  const userId = await getUserId();

  const files = await db.query.pdfs.findMany({
    where: (model, { eq }) => eq(model.userId, userId),
    orderBy: (model, { desc }) => desc(model.id),
  });
  return files;
}

export async function getPattern(id: number) {
  const userId = await getUserId();

  const pattern = await db.query.pdfs.findFirst({
    where: (model, { and, eq }) =>
      and(eq(model.id, id), eq(model.userId, userId)),
  });

  if (!pattern) throw new Error("Pattern not found");
  return pattern;
}

export async function getPatternDetails(id: number) {
  const pattern = await getPattern(id);

  const [seasons, sizes, tags] = await Promise.all([
    db.query.pdfSeasons.findMany({
      where: (model, { eq }) => eq(model.pdfId, id),
      with: { season: true },
    }),
    db.query.pdfSizes.findMany({
      where: (model, { eq }) => eq(model.pdfId, id),
      with: { size: true },
    }),
    db.query.pdfTags.findMany({
      where: (model, { eq }) => eq(model.pdfId, id),
      with: { tag: true },
    }),
  ]);

  return {
    ...pattern,
    seasons: seasons.map((s) => s.season.name),
    sizes: sizes.map((s) => s.size.name),
    tags: tags.map((t) => t.tag.name),
  };
}

export async function deleteFile(id: number) {
  const userId = await getUserId();
  await db.delete(pdfs).where(and(eq(pdfs.id, id), eq(pdfs.userId, userId)));

  analyticsServerClient.capture({
    event: "delete pdf",
    distinctId: userId,
    properties: {
      fileId: id,
    },
  });
}
