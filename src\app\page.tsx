import { SignedIn, SignedOut } from "@clerk/nextjs";
import { auth } from "@clerk/nextjs/server";
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from "@tanstack/react-query";
import { FileGallery } from "~/features/files/components/gallery";
import { getUserFiles } from "~/server/queries";
import { QUERY_KEYS } from "~/lib/constants";

//export const dynamic = "force-dynamic";
export const revalidate = 60; // Revalidate every minute

export default async function HomePage() {
  const queryClient = new QueryClient();
  const { userId } = await auth();

  if (userId) {
    try {
      await queryClient.prefetchQuery({
        queryKey: QUERY_KEYS.FILES,
        queryFn: getUserFiles,
      });
    } catch (error) {
      console.error("Error prefetching files:", error);
      throw error;
    }
  }

  return (
    <main>
      <SignedOut>
        <div className="h-full w-full text-center text-2xl">
          Please sign in to view this page
        </div>
      </SignedOut>
      <SignedIn>
        <HydrationBoundary state={dehydrate(queryClient)}>
          <FileGallery />
        </HydrationBoundary>
      </SignedIn>
    </main>
  );
}
