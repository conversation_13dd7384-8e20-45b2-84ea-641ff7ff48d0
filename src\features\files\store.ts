import { create } from "zustand";
import type { BasicFile } from "~/app/api/files/types";

interface FilesStore<T = number> {
  selectedFileIds: Set<T>;
  currentFile: BasicFile | null;
  actions: {
    toggleFileSelect: (id: T) => void;
    setSelectedFileIds: (ids: T[]) => void;
    setCurrentFile: (file: BasicFile | null) => void;
    clearSelectedFiles: () => void;
  };
}

export const useFilesStore = create<FilesStore>((set, get) => ({
  selectedFileIds: new Set<number>(),
  currentFile: null,
  actions: {
    toggleFileSelect: (id) =>
      set((state) => {
        const newSet = new Set(state.selectedFileIds);
        if (newSet.has(id)) {
          newSet.delete(id);
        } else {
          newSet.add(id);
        }
        return { selectedFileIds: newSet };
      }),
    setSelectedFileIds: (ids) => set({ selectedFileIds: new Set(ids) }),
    setCurrentFile: (file) => set({ currentFile: file }),
    clearSelectedFiles: () => set({ selectedFileIds: new Set() }),
  },
}));

// Custom hooks
export const useSelectedFileIds = () =>
  useFilesStore((state) => state.selectedFileIds);
export const useFilesStoreActions = () =>
  useFilesStore((state) => state.actions);
export const useFileIsSelected = (id: number): boolean =>
  useFilesStore((state) => state.selectedFileIds.has(id));
export const useSelectedFilesCount = (): number =>
  useFilesStore((state) => state.selectedFileIds.size);
export const useCurrentFile = () => useFilesStore((state) => state.currentFile);
