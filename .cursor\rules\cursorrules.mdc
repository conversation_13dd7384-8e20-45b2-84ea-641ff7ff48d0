---
description:
globs:
alwaysApply: true
---
{
  "typescript": {
    "rules": {
      "preferConstOverLet": true,
      "preferArrowFunctions": true,
      "preferOptionalChaining": true,
      "preferNullishCoalescing": true,
      "preferTemplate": true,
      "preferExplicitReturnType": true,
      "noExplicitAny": true,
      "noNonNullAssertion": true,
      "useDescriptiveFunctionNames": {
        "description": "Name functions based on what they do, not what they are (e.g., 'calculateTotalPrice' not 'priceFunction')",
        "value": true
      },
      "useVerbNounNaming": {
        "description": "Begin function names with verbs followed by nouns (e.g., 'fetchUserData', 'validateEmail', 'calculateTotal')",
        "value": true
      },
      "avoidRedundantComments": {
        "description": "Don't comment on what is already obvious from the function name or code itself",
        "value": true
      }
    }
  },
  "react": {
    "rules": {
      "useReactFunctionComponents": true,
      "useHookAtTopLevel": true,
      "useFragmentSyntax": true,
      "useDestructuring": true,
      "useMemoForExpensiveCalculations": true,
      "useCallbackForHandlers": true,
      "useNamedExports": true,
      "useActionVerbForEventHandlers": {
        "description": "Event handlers should begin with action verbs (e.g., 'handleClick', 'processPurchase', 'validateForm')",
        "value": true
      }
    }
  },
  "nextjs": {
    "rules": {
      "useServerComponents": true,
      "useClientDirective": true,
      "useNextImage": true,
      "useNextLink": true,
      "useMetadata": true,
      "useConventionFiles": {
        "description": "Utilize App Router special files like layout.tsx, page.tsx, loading.tsx, error.tsx, template.tsx, and route handlers where appropriate.",
        "value": true
      },
      "useServerActions": true
    }
  },
  "fileStructure": {
    "rules": {
      "componentNaming": {
        "pattern": "PascalCase",
        "description": "Component files should use PascalCase (e.g., Button.tsx)"
      },
      "utilityNaming": {
        "pattern": "camelCase",
        "description": "Utility files should use camelCase (e.g., formatDate.ts)"
      },
      "hookNaming": {
        "pattern": "camelCase",
        "prefix": "use",
        "description": "Hooks should be prefixed with 'use' and use camelCase (e.g., useAuth.ts)"
      },
      "queryHookNaming": {
        "pattern": "camelCase",
        "prefix": "use",
        "description": "React Query hooks should be grouped in files like queries.ts"
      },
      "controllerNaming": {
        "pattern": "camelCase",
        "prefix": "use",
        "suffix": "Controller",
        "description": "Controller hooks should be named useXXXController (e.g., useFilesActionController.ts)"
      },
      "componentStructure": {
        "description": "Components should be organized in a feature-based structure",
        "structure": [
          {
            "path": "src/features/[feature-name]/components/",
            "description": "Feature-specific components organized by feature"
          },
          {
            "path": "src/components/ui/",
            "description": "Reusable UI components (shadcn/radix)"
          },
          {
            "path": "src/components/layout/",
            "description": "Layout components used across features"
          },
          {
            "path": "src/components/",
            "description": "Utility components used across features"
          },
          {
            "path": "src/app/[route]/_components/",
            "description": "Route-specific components that are only used within a specific route"
          }
        ]
      },
      "featureStructure": {
        "description": "Features should follow a consistent structure",
        "structure": [
          {
            "path": "src/features/[feature-name]/components/",
            "description": "Components specific to the feature"
          },
          {
            "path": "src/features/[feature-name]/hooks/",
            "description": "Hooks specific to the feature, including queries and controllers"
          },
          {
            "path": "src/features/[feature-name]/api/",
            "description": "API functions specific to the feature"
          },
          {
            "path": "src/features/[feature-name]/store.ts",
            "description": "Zustand store specific to the feature (if needed)"
          }
        ]
      },
      "hooksLocation": {
        "path": "src/features/[feature-name]/hooks/",
        "description": "Feature-specific hooks should be placed in the feature's hooks directory"
      },
      "queriesLocation": {
        "path": "src/features/[feature-name]/hooks/queries.ts",
        "description": "React Query hooks should be organized in the feature's hooks directory"
      },
      "utilsLocation": {
        "path": "src/lib/",
        "description": "Utility functions should be placed in the lib directory"
      },
      "apiRoutesLocation": {
        "path": "src/app/api/",
        "description": "Next.js API routes should be placed in the app/api directory"
      },
      "featureApiLocation": {
        "path": "src/features/[feature-name]/api/",
        "description": "Feature-specific API functions should be placed in the feature's api directory"
      },
      "serverLocation": {
        "path": "src/server/",
        "description": "Server-side code should be placed in the server directory"
      }
    }
  },
  "controllerPattern": {
    "rules": {
      "useControllerForComplexLogic": true,
      "combineQueriesAndStoreInControllers": true,
      "returnCleanInterfaceFromControllers": true,
      "useCallbackInControllers": true,
      "description": "Use controller hooks to combine React Query and Zustand state management"
    }
  },
  "styling": {
    "rules": {
      "useTailwindClasses": true,
      "useCnUtility": true,
      "useShadcnComponents": true,
      "preferThemeVariables": {
        "description": "Prefer theme variables (e.g., text-primary, bg-background) over hardcoded values (e.g., text-blue-500) for colors, spacing, etc., to ensure theme consistency.",
        "value": true
      },
      "useResponsiveDesign": true
    }
  },
  "stateManagement": {
    "rules": {
      "useZustandForGlobalState": {
        "description": "Use Zustand for global state that needs to be shared across distant components or requires advanced store features",
        "value": true
      },
      "useReactQueryForServerState": true,
      "useLocalStateForComponentState": {
        "description": "Use local component state for ephemeral UI concerns specific to a single component; use Zustand for shared state that affects multiple components or needs to persist",
        "examples": [
          "Use local state for form input values during typing, toggle states for dropdowns, animation states",
          "Use Zustand for user preferences, selected tabs that should persist, multi-step form state spanning components",
          "Consider state ownership and lifecycle when deciding where state should live"
        ],
        "value": true
      },
      "useContextForSharedState": true,
      "balanceStateApproaches": {
        "description": "Choose the appropriate state management approach based on the nature and scope of the state, considering both immediate component needs and application-wide concerns",
        "value": true
      }
    }
  },
  "reactQuery": {
    "rules": {
      "useQueryClient": {
        "description": "Use QueryClient and QueryClientProvider at the root of your app for global configuration",
        "value": true
      },
      "implementCustomHooks": {
        "description": "Create custom hooks for queries and mutations to encapsulate data fetching logic",
        "value": true
      },
      "structureQueryKeys": {
        "description": "Use array-based query keys with consistent structure: [entity, identifier, params]",
        "value": true
      },
      "usePrefetching": {
        "description": "Prefetch only small-to-medium payloads (<100–200 KB) that speed up critical UX; skip or defer very large queries to avoid bloating initial HTML and wasting bandwidth",
        "value": true
      },
      "implementDataTransformation": {
        "description": "Use the select option to transform data at the query level rather than in components",
        "value": true
      },
      "handlePagination": {
        "description": "Use useInfiniteQuery for infinite scrolling and implement proper pagination patterns",
        "value": true
      },
      "implementOptimisticUpdates": {
        "description": "Use optimistic updates with onMutate, onError, and onSettled for responsive UIs",
        "value": true
      },
      "useQueryInvalidation": {
        "description": "Implement strategic query invalidation to maintain data consistency across the app",
        "value": true
      },
      "considerSuspenseMode": {
        "description": "Consider using React Query's suspense mode for loading states when appropriate",
        "value": true
      },
      "implementBackgroundRefetching": {
        "description": "Configure staleTime and refetchOnWindowFocus for appropriate background data refreshing",
        "value": true
      },
      "useStaleWhileRevalidateStrategy": {
        "description": "Use stale-while-revalidate strategy by configuring staleTime based on data volatility to balance immediate UI rendering with data freshness",
        "value": true
      },
      "useDevTools": {
        "description": "Implement React Query DevTools in development environment for debugging",
        "value": true
      }
    }
  },
  "imports": {
    "rules": {
      "groupImports": true,
      "orderImports": [
        "react",
        "next",
        "external libraries",
        "~/features",
        "~/components",
        "~/lib",
        "~/server",
        "relative imports"
      ],
      "useAliasImports": true,
      "preferDestructuring": true,
      "noUnusedImports": true
    }
  },
  "documentation": {
    "rules": {
      "useJSDoc": true,
      "documentExportedFunctions": true,
      "documentComponents": true,
      "documentHooks": true,
      "documentControllers": true,
      "documentTypes": true,
      "documentStores": true,
      "commentOnlyNonObviousCode": {
        "description": "Add comments only to explain 'why' something is done, not 'what' is being done when it's obvious from the code",
        "value": true
      },
      "useDocCommentsForAPI": {
        "description": "Use JSDoc comments for public APIs, but prefer self-documenting code for implementation details",
        "value": true
      },
      "useSelfExplanatoryNames": {
        "description": "Prefer descriptive names over comments to explain intent (e.g., 'getUsersWithActivePlan' instead of 'getUsers' with a comment)",
        "value": true
      },
      "avoidObviousSectionComments": {
        "description": "Don't add comments that merely label obvious sections of code (e.g., '// Query Keys' before query key definitions). Use proper code organization and spacing instead.",
        "value": true
      },
      "focusOnWhyNotWhat": {
        "description": "Comments should explain WHY code exists or works a certain way, not WHAT it does (which should be clear from the code itself)",
        "examples": [
          {
            "good": "// Using a separate worker to avoid blocking the main thread during heavy PDF processing",
            "bad": "// Process the PDF in a worker"
          }
        ],
        "value": true
      },
      "provideContextInJSDoc": {
        "description": "JSDoc comments should provide usage context, constraints, and edge cases - not just restate parameter types",
        "examples": [
          {
            "good": "/**\n * Uploads files to cloud storage with automatic retry logic\n * Handles files up to 100MB and automatically generates thumbnails\n * @param files - Files from an input element (max 5 files)\n * @returns Upload results with public URLs and metadata\n */",
            "bad": "/**\n * Upload files\n * @param files - The files\n * @returns The result\n */"
          }
        ],
        "value": true
      },
      "avoidRedundantTypeComments": {
        "description": "Don't add comments that merely restate type information that's already in the TypeScript type definitions",
        "examples": [
          {
            "good": "// Cache expiration is in seconds to match the API's expectations",
            "bad": "// The timeout is a number"
          }
        ],
        "value": true
      }
    }
  },
  "performance": {
    "rules": {
      "useMemoization": true,
      "useVirtualization": true,
      "useImageOptimization": true,
      "useLazyLoading": true,
      "useCodeSplitting": true,
      "optimizeQueryStaleTime": {
        "description": "Tune staleTime by data volatility: 0–30s for live feeds, 1–5min for moderately dynamic data, 10min+ for nearly static data. Balances instant cache hits with background revalidation",
        "value": true
      },
      "implementQueryCaching": {
        "description": "Leverage React Query's caching mechanisms with proper cache time configuration",
        "value": true
      },
      "useQuerySelectorsEfficiently": {
        "description": "Use select option to extract only needed data from query results to minimize re-renders",
        "value": true
      },
      "avoidRedundantQueries": {
        "description": "Structure components to avoid duplicate or redundant query instances",
        "value": true
      }
    }
  },
  "errorHandling": {
    "rules": {
      "useErrorBoundaries": {
        "description": "Wrap components or sections prone to errors with React Error Boundaries for graceful degradation.",
        "value": true
      },
      "handleApiErrorsConsistently": {
        "description": "Implement a consistent strategy for handling and displaying errors from API calls (React Query onError, try/catch).",
        "value": true
      },
      "implementErrorLogging": {
        "description": "Integrate error logging/reporting service to track client-side and server-side errors.",
        "value": true
      },
      "configureQueryErrorHandling": {
        "description": "Configure global error handling for React Query in the QueryClient options",
        "value": true
      },
      "useQueryErrorResetters": {
        "description": "Implement reset functionality for query errors to allow users to retry failed operations",
        "value": true
      },
      "implementRetryLogic": {
        "description": "Configure appropriate retry logic for queries based on error types and network conditions",
        "value": true
      }
    }
  },
  "accessibility": {
    "rules": {
      "useSemanticHTML": {
        "description": "Prefer semantic HTML elements (e.g., <nav>, <main>, <button>) over generic divs/spans.",
        "value": true
      },
      "ensureKeyboardNavigable": {
        "description": "All interactive elements must be focusable and operable via keyboard.",
        "value": true
      },
      "provideAccessibleLabels": {
        "description": "Ensure all form inputs, buttons, and significant interactive elements have accessible labels (aria-label, aria-labelledby, or associated <label>).",
        "value": true
      },
      "useAriaWhenNecessary": {
        "description": "Use ARIA attributes appropriately to bridge gaps in semantics for complex widgets or dynamic content.",
        "value": true
      }
    }
  },
  "pdfHandling": {
    "rules": {
      "usePdfJsForRendering": {
        "description": "Use PDF.js for client-side PDF rendering and avoid duplicating configuration code across components",
        "value": true
      },
      "handlePdfLoadingStates": {
        "description": "Implement proper loading states for PDF rendering with appropriate fallbacks, using thumbnail images as background during PDF loading rather than showing a blank background",
        "value": true
      }
    }
  },
  "theming": {
    "rules": {
      "useOklchColorFormat": {
        "description": "The project uses oklch color format in CSS variables for consistent theming across light and dark modes",
        "value": true
      }
    }
  },
  "testing": {
    "rules": {
      "implementUnitTests": {
        "description": "Write unit tests for critical utility functions, hooks, and complex component logic.",
        "value": true
      },
      "implementIntegrationTests": {
        "description": "Write integration tests for components and features involving multiple units (e.g., form submissions, data fetching interactions).",
        "value": true
      },
      "considerEndToEndTests": {
        "description": "Consider E2E tests for critical user flows.",
        "value": true
      }
    }
  },
  "codeStyle": {
    "rules": {
      "avoidRedundantComments": {
        "description": "Avoid comments that merely restate what the code already clearly expresses",
        "value": true
      },
      "useStructuralOrganization": {
        "description": "Use structural organization (spacing, grouping related code) rather than comment headers to separate code sections",
        "value": true
      },
      "preferSelfDocumentingCode": {
        "description": "Write code that is self-documenting through clear naming and structure, minimizing the need for explanatory comments",
        "value": true
      }
    }
  },
  "componentDesign": {
    "rules": {
      "meaningfulAbstraction": {
        "description": "Suggest abstractions that reduce cognitive load, not ones that merely reorganize it. A component or hook should exist because it simplifies the mental model, not because it follows a pattern.",
        "examples": [
          "Extract a hook when state logic has clear boundaries and its own lifecycle",
          "Keep simple UI structures inline with clear comments rather than creating new components",
          "Name abstractions based on their conceptual role in the application"
        ],
        "value": true
      },
      "intentionalComponentDesign": {
        "description": "Components should be designed with clear boundaries and a single conceptual responsibility. The API surface (props) should tell a story about the component's purpose.",
        "examples": [
          "Props should form a coherent, minimal interface",
          "Component names should communicate intent and responsibility",
          "Component boundaries should align with natural seams in the UI and behavior"
        ],
        "value": true
      },
      "compositionOverConfiguration": {
        "description": "Prefer composition of smaller, focused components over configurable mega-components. Props should rarely be used to dramatically alter component behavior.",
        "examples": [
          "Break large components into composable pieces",
          "Use children and render props for flexible composition",
          "Avoid boolean props that toggle significant behavioral changes"
        ],
        "value": true
      },
      "optimizedForChange": {
        "description": "Code should be organized to make the most likely changes easy and isolated. Anticipate the dimensions along which the code will evolve.",
        "examples": [
          "Group code by feature/domain first, then by technical role",
          "Design components with clear extension points",
          "Isolate volatile parts from stable parts"
        ],
        "value": true
      },
      "contextGathering": {
        "description": "Before suggesting significant changes, gather comprehensive information about the current implementation, related files, and the broader architecture.",
        "examples": [
          "Analyze component relationships before suggesting restructuring",
          "Understand the data flow before modifying state management",
          "Review existing patterns before introducing new ones"
        ],
        "value": true
      },
      "explainReasoning": {
        "description": "When suggesting architectural changes, explain the reasoning and tradeoffs to help the developer make informed decisions.",
        "examples": [
          "Explain why a hook extraction would improve maintainability",
          "Discuss tradeoffs between different component composition approaches",
          "Clarify the benefits of a proposed type structure"
        ],
        "value": true
      }
    }
  },
  "typeSystem": {
    "rules": {
      "typeAsDocumentation": {
        "description": "Use the type system as documentation that can't become outdated. Types should express intent and constraints, not just structure.",
        "examples": [
          "Create domain-specific types rather than using primitives",
          "Use union types to express valid states",
          "Avoid type assertions except in boundary cases with clear comments"
        ],
        "value": true
      },
      "domainDrivenTypes": {
        "description": "Types should reflect domain concepts and business rules, not just data structures.",
        "examples": [
          "Create types that represent domain entities and value objects",
          "Use discriminated unions to model state transitions",
          "Express validation rules and constraints in types when possible"
        ],
        "value": true
      },
      "exhaustiveTypeChecking": {
        "description": "Use TypeScript's type system to ensure all possible cases are handled, especially with unions and discriminated unions.",
        "examples": [
          "Use exhaustive switch statements with never type checks",
          "Handle all possible states in conditional rendering",
          "Use type guards to narrow types safely"
        ],
        "value": true
      },
      "progressiveTypeRefinement": {
        "description": "Start with simple types and refine them as understanding improves. Types should evolve with the codebase.",
        "examples": [
          "Refactor types when new requirements emerge",
          "Split overly broad types into more specific ones",
          "Consolidate similar types when patterns emerge"
        ],
        "value": true
      }
    }
  }
}

