# Component Organization

This document outlines the component organization strategy used in the PitterPattern project.

## Feature-Based Organization

The project follows a feature-based organization approach, where components, hooks, and API functions are grouped by feature rather than by type.

### Feature Structure

Each feature follows a consistent structure:

```
src/
  features/
    [feature-name]/
      components/
        index.ts         # Exports all components
        [Component].tsx  # Individual components
      hooks/
        index.ts         # Exports all hooks
        use[Hook].ts     # Individual hooks
      api/
        index.ts         # API functions
      store.ts           # (Optional) Zustand store
```

### Current Features

1. **Files Feature**
   - Purpose: Display and manage files
   - Components: FileGallery, FilterBar, FileDetailsView, PDFViewer, etc.
   - Hooks: useFiles, useFile, useDeleteFile, useFilesActionController
   - API: fetchFiles, fetchFile, deleteFile

2. **Upload Feature**
   - Purpose: Handle file uploads
   - Components: PdfUploadButton
   - Hooks: useUploadThingInputProps, getMultFilesData, handleUploadedFiles

## Non-Feature Components

Components that don't belong to a specific feature are organized as follows:

1. **UI Components**
   - Location: `src/components/ui/`
   - Purpose: Reusable UI components (shadcn/radix)
   - Examples: Button, Card, Dialog, etc.

2. **Layout Components**
   - Location: `src/components/layout/`
   - Purpose: Page layout components
   - Examples: TopNav

3. **Utility Components**
   - Location: `src/components/`
   - Purpose: Utility components used across features
   - Examples: ErrorBoundary, ThemeToggle

## App Router Components

Components specific to Next.js App Router routes are organized as follows:

1. **Route Components**
   - Location: `src/app/[route]/page.tsx`
   - Purpose: Page components for specific routes

2. **Route-Specific Components**
   - Location: `src/app/[route]/_components/`
   - Purpose: Components used only within a specific route

## Naming Conventions

1. **Component Files**
   - PascalCase for component files (e.g., `Button.tsx`)
   - Component name should match file name

2. **Hook Files**
   - camelCase with 'use' prefix (e.g., `useFiles.ts`)

3. **API Files**
   - camelCase (e.g., `fetchFiles.ts`)

4. **Index Files**
   - Used for exporting components/hooks from directories
   - Not used for main component implementations

## Import Conventions

1. **Feature Imports**
   - Import from feature index files when possible
   - Example: `import { PdfUploadButton } from "~/features/upload/components";`

2. **UI Component Imports**
   - Import directly from component files
   - Example: `import { Button } from "~/components/ui/button";`
