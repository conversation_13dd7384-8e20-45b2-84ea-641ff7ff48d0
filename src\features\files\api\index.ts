import type { BasicFile, FileList } from "~/app/api/files/types";

import axios from "axios";
import {
  basicFileListSchema,
  basicFileSchema,
  patternDetailsSchema,
} from "~/app/api/files/schema";
import type { PatternAnalysis } from "~/lib/api/external/ai-file-info/generate-pdf-analysis";

export async function fetchFiles(): Promise<FileList> {
  try {
    const response = await axios.get("/api/files");
    console.log("fetch resp:", response);
    const result = basicFileListSchema.safeParse(response.data);

    if (!result.success) {
      console.error("API response validation failed:", result.error);
      throw new Error("Invalid API response format");
    }
    console.log("fetch result:", result);
    return result.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(`HTTP error! status: ${error.response?.status}`);
    }
    throw error;
  }
}

export async function fetchFile(id: number): Promise<BasicFile> {
  try {
    const response = await axios.get(`/api/files/${id}`);
    const result = basicFileSchema.safeParse(response.data);

    if (!result.success) {
      console.error("API response validation failed:", result.error);
      throw new Error("Invalid API response format");
    }

    return result.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(`HTTP error! status: ${error.response?.status}`);
    }
    throw error;
  }
}

export async function fetchPatternDetails(
  fileId: number,
): Promise<PatternAnalysis> {
  try {
    const response = await axios.get(`/api/files/${fileId}/details`);

    const result = patternDetailsSchema.safeParse(response.data);
    if (!result.success) {
      console.error("API response validation failed:", result.error);
      throw new Error("Invalid API response format");
    }

    return result.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(
        `Failed to fetch pattern details: ${error.response?.status} ${error.response?.statusText}`,
      );
    }
    throw new Error(`Failed to fetch pattern details: ${String(error)}`);
  }
}

export async function deleteFile(id: number): Promise<void> {
  try {
    await axios.delete(`/api/files/${id}`);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(`HTTP error! status: ${error.response?.status}`);
    }
    throw error;
  }
}
