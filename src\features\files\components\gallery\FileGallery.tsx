"use client";

import { useState, useMemo, useCallback, memo } from "react";
import Link from "next/link";
import { FilterBar } from "./FilterBar";
import { useFiles } from "~/features/files/hooks/queries";
import { useQueryClient } from "@tanstack/react-query";
import type { BasicFile } from "~/app/api/files/types";
import ErrorBoundary from "~/components/ErrorBoundary";
import { FilterBarSkeleton } from "./FilterBarSkeleton";
import { ThumbnailCard, GallerySkeleton } from ".";

export type FilterKey = keyof Omit<
  BasicFile,
  | "id"
  | "url"
  | "userId"
  | "createdAt"
  | "updatedAt"
  | "author"
  | "authorSite"
  | "name"
  | "thumbnailUrl"
>;

export type FilterState = {
  search: string;
} & {
  [K in FilterKey]?: string;
};

export type CategoryFilterData = Record<FilterKey, string[]>;

function isFilterKey(key: string): key is FilterKey {
  return [
    "mainCategory",
    "difficulty",
    "garmentType",
    "yarnWeight",
    "ageGroup",
  ].includes(key);
}

const LoadingState = () => (
  <>
    <FilterBarSkeleton />
    <GallerySkeleton />
  </>
);

const EmptyState = ({
  categoryFilterData,
  onFilterChange,
  onSortChange,
}: {
  categoryFilterData: CategoryFilterData;
  onFilterChange: (filters: FilterState) => void;
  onSortChange: (sort: string) => void;
}) => (
  <>
    <FilterBar
      onFilterChange={onFilterChange}
      onSortChange={onSortChange}
      categoryFilterData={categoryFilterData}
    />
    <div className="flex h-64 w-full items-center justify-center p-8 text-center">
      <div className="rounded-lg border bg-card p-8 shadow-sm">
        <h3 className="text-xl font-medium">No patterns found</h3>
        <p className="mt-2 text-muted-foreground">
          Upload a pattern to get started or try different filter settings.
        </p>
      </div>
    </div>
  </>
);

// Define the component, then memoize it
const FilesListComponent = ({
  files,
  categoryFilterData,
  onFilterChange,
  onSortChange,
}: {
  files: BasicFile[];
  categoryFilterData: CategoryFilterData;
  onFilterChange: (filters: FilterState) => void;
  onSortChange: (sort: string) => void;
}) => (
  <>
    <FilterBar
      onFilterChange={onFilterChange}
      onSortChange={onSortChange}
      categoryFilterData={categoryFilterData}
    />
    <div className="flex flex-wrap justify-center gap-4 p-4">
      {files.map((file) => (
        <div key={file.id} className="w-48">
          <div className="h-64 w-48">
            <Link href={`/file/${file.id}`} className="block h-full w-full">
              <ThumbnailCard
                src={file.thumbnailUrl}
                name={file.name}
                author={file.author}
                category={file.mainCategory}
                difficulty={file.difficulty}
                showLabels={true}
                className="h-full w-full"
                pdfUrl={file.url}
              />
            </Link>
          </div>
        </div>
      ))}
    </div>
  </>
);

// Memoize the component to prevent unnecessary re-renders
const FilesList = memo(FilesListComponent);

export function FileGallery() {
  const { files, isLoading } = useFiles();

  const [filters, setFilters] = useState<FilterState>({
    search: "",
  });

  const shouldProcessFiles = files?.length;

  const categoryFilterData: CategoryFilterData = useMemo(() => {
    if (!shouldProcessFiles)
      return {
        mainCategory: ["All"],
        difficulty: ["All"],
        garmentType: ["All"],
        yarnWeight: ["All"],
        ageGroup: ["All"],
      };
    return {
      mainCategory: ["All", ...new Set(files.map((file) => file.mainCategory))],
      difficulty: ["All", ...new Set(files.map((file) => file.difficulty))],
      garmentType: ["All", ...new Set(files.map((file) => file.garmentType))],
      yarnWeight: ["All", ...new Set(files.map((file) => file.yarnWeight))],
      ageGroup: ["All", ...new Set(files.map((file) => file.ageGroup))],
    };
  }, [files, shouldProcessFiles]);

  const [sortOrder, setSortOrder] = useState<string>("");

  // TODO - see if some of this logic can be extracted and tested
  const filteredAndSortedFiles = useMemo(() => {
    if (!shouldProcessFiles) return [];
    let result = files;

    // Apply filters
    if (filters.search) {
      result = result.filter((file) =>
        file.name.toLowerCase().includes(filters.search.toLowerCase()),
      );
    }

    Object.entries(filters).forEach(([key, value]) => {
      if (isFilterKey(key) && value !== "All") {
        result = result.filter((file) => file[key] === value);
      }
    });

    // Apply sorting
    switch (sortOrder) {
      case "name_asc":
        result.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case "name_desc":
        result.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case "date_asc":
        result.sort(
          (a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );
        break;
      case "date_desc":
        result.sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
        );
        break;
    }

    return result;
  }, [files, shouldProcessFiles, filters, sortOrder]);

  const handleFilterChange = useCallback((newFilters: FilterState) => {
    setFilters(newFilters);
  }, []);

  const handleSortChange = useCallback((sort: string) => {
    setSortOrder(sort);
  }, []);

  return (
    <ErrorBoundary
      fallback={<div>Something went wrong. Please try again later.</div>}
    >
      <div className="duration-500 animate-in fade-in">
        {isLoading ? (
          <LoadingState />
        ) : !files?.length ? (
          <EmptyState
            categoryFilterData={categoryFilterData}
            onFilterChange={handleFilterChange}
            onSortChange={handleSortChange}
          />
        ) : (
          <FilesList
            files={filteredAndSortedFiles}
            categoryFilterData={categoryFilterData}
            onFilterChange={handleFilterChange}
            onSortChange={handleSortChange}
          />
        )}
      </div>
    </ErrorBoundary>
  );
}
