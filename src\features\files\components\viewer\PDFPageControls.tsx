/**
 * PDFPageControls - Navigation controls for PDF viewing
 *
 * This component provides a UI for navigating between PDF pages:
 * - Shows current page number and total pages
 * - Provides previous/next buttons with proper disabled states
 * - Supports showing/hiding based on mouse hover
 */

import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "~/components/ui/button";

interface PDFPageControlsBaseProps {
  /** Current page being displayed */
  currentPage: number;

  /** Total number of pages in the document */
  totalPages: number;

  /** Handler for navigating to previous page */
  onPrevious: () => void;

  /** Handler for navigating to next page */
  onNext: () => void;
}

interface PDFPageControlsProps extends PDFPageControlsBaseProps {
  /** Whether the controls should be visible */
  isVisible: boolean;
}

/**
 * Navigation controls for PDF viewing that appear on hover
 */
export const PDFPageControls: React.FC<PDFPageControlsProps> = ({
  isVisible,
  currentPage,
  totalPages,
  onPrevious,
  onNext,
}) => {
  // Determine if navigation buttons should be enabled
  const canGoPrevious = currentPage > 1;
  const canGoNext = currentPage < totalPages;

  return (
    <div
      className={`bg-background/80 absolute bottom-4 left-1/2 z-10 flex -translate-x-1/2 items-center gap-2 rounded-lg p-2 shadow-lg backdrop-blur-sm transition-opacity duration-300 ${
        isVisible ? "opacity-100" : "opacity-0"
      }`}
    >
      {/* Previous page button - disabled on first page */}
      <Button
        variant="outline"
        size="icon"
        onClick={onPrevious}
        disabled={!canGoPrevious}
      >
        <ChevronLeft className="h-4 w-4" />
        <span className="sr-only">Previous page</span>
      </Button>

      {/* Page counter display */}
      <div className="text-sm font-medium">
        {currentPage} / {totalPages}
      </div>

      {/* Next page button - disabled on last page */}
      <Button
        variant="outline"
        size="icon"
        onClick={onNext}
        disabled={!canGoNext}
      >
        <ChevronRight className="h-4 w-4" />
        <span className="sr-only">Next page</span>
      </Button>
    </div>
  );
};
