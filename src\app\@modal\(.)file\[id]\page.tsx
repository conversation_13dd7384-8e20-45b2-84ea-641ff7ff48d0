import { FileDetailsView } from "~/features/files/components/details-view";
import { Modal } from "./modal";

export default async function PDFModal({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: fileId } = await params;

  const idAsNumber = Number(fileId);
  if (isNaN(idAsNumber)) throw new Error("Invalid file ID");

  return (
    <Modal>
      <FileDetailsView fileId={idAsNumber} />
    </Modal>
  );
}
